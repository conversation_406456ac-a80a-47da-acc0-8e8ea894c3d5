<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin Dashboard - Freelancer Platform</title>
    <script
      src="https://code.jquery.com/jquery-3.4.1.js"
      integrity="sha256-WpOohJOqMqqyKL9FccASB9O0KwACQJpFTUBLTYOVvVU="
      crossorigin="anonymous"
    ></script>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🛡️ Admin Dashboard</h1>
        <div class="admin-info">
          <span id="adminName">Loading...</span>
          <button onclick="logout()" class="logout-btn">Logout</button>
        </div>
      </div>

      <div class="tabs">
        <button class="tab-button active" onclick="showTab('users')">
          All Users
        </button>
        <button class="tab-button" onclick="showTab('freelancers')">
          Freelancers
        </button>
        <button class="tab-button" onclick="showTab('stats')">
          Statistics
        </button>
      </div>

      <div id="users-tab" class="tab-content active">
        <div class="section-header">
          <h2>All Users</h2>
          <div class="stats-cards">
            <div class="stat-card">
              <div class="stat-number" id="totalUsers">0</div>
              <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" id="activeUsers">0</div>
              <div class="stat-label">Active Users</div>
            </div>
          </div>
        </div>
        <div class="table-container">
          <table id="usersTable">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Role</th>
                <th>Status</th>
                <th>Joined</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <!-- Users will be loaded here -->
            </tbody>
          </table>
        </div>
      </div>

      <div id="freelancers-tab" class="tab-content">
        <div class="section-header">
          <h2>Freelancers</h2>
          <div class="stats-cards">
            <div class="stat-card">
              <div class="stat-number" id="totalFreelancers">0</div>
              <div class="stat-label">Total Freelancers</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" id="activeFreelancers">0</div>
              <div class="stat-label">Active Freelancers</div>
            </div>
          </div>
        </div>
        <div class="table-container">
          <table id="freelancersTable">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Location</th>
                <th>Skills</th>
                <th>Rate</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <!-- Freelancers will be loaded here -->
            </tbody>
          </table>
        </div>
      </div>

      <div id="stats-tab" class="tab-content">
        <div class="section-header">
          <h2>Platform Statistics</h2>
        </div>
        <div class="stats-grid">
          <div class="stat-card large">
            <div class="stat-number" id="totalPlatformUsers">0</div>
            <div class="stat-label">Total Platform Users</div>
          </div>
          <div class="stat-card large">
            <div class="stat-number" id="newUsersToday">0</div>
            <div class="stat-label">New Users Today</div>
          </div>
          <div class="stat-card large">
            <div class="stat-number" id="freelancerPercentage">0%</div>
            <div class="stat-label">Freelancer Ratio</div>
          </div>
        </div>
      </div>
    </div>

    <script>
      let currentUser = null;

      // Check if user is admin
      function checkAdminAuth() {
        $.ajax({
          url: "/api/auth/status",
          type: "GET",
          success: function (response) {
            if (response.authenticated && response.user.role === "admin") {
              currentUser = response.user;
              document.getElementById(
                "adminName"
              ).textContent = `Welcome, ${currentUser.name}`;
              loadUsers();
              loadFreelancers();
              loadStats();
            } else {
              alert("Admin access required");
              window.location.href = "/login.html";
            }
          },
          error: function () {
            alert("Authentication failed");
            window.location.href = "/login.html";
          },
        });
      }

      function showTab(tabName) {
        // Hide all tabs
        document.querySelectorAll(".tab-content").forEach((tab) => {
          tab.classList.remove("active");
        });
        document.querySelectorAll(".tab-button").forEach((btn) => {
          btn.classList.remove("active");
        });

        // Show selected tab
        document.getElementById(tabName + "-tab").classList.add("active");
        event.target.classList.add("active");
      }

      function loadUsers() {
        $.ajax({
          url: "/api/admin/users",
          type: "GET",
          success: function (users) {
            const tbody = document.querySelector("#usersTable tbody");
            tbody.innerHTML = "";

            let totalUsers = users.length;
            let activeUsers = users.filter((u) => u.isActive).length;

            document.getElementById("totalUsers").textContent = totalUsers;
            document.getElementById("activeUsers").textContent = activeUsers;

            users.forEach((user) => {
              const row = document.createElement("tr");
              row.innerHTML = `
                <td>${user.name}</td>
                <td>${user.email}</td>
                <td><span class="role-badge ${user.role}">${
                user.role
              }</span></td>
                <td><span class="status-badge ${
                  user.isActive ? "active" : "inactive"
                }">${user.isActive ? "Active" : "Inactive"}</span></td>
                <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                <td>
                  <button onclick="toggleUserStatus('${
                    user._id
                  }', ${!user.isActive})" class="action-btn ${
                user.isActive ? "deactivate" : "activate"
              }">
                    ${user.isActive ? "Deactivate" : "Activate"}
                  </button>
                  <button onclick="deleteUser('${
                    user._id
                  }')" class="action-btn delete">Delete</button>
                </td>
              `;
              tbody.appendChild(row);
            });
          },
          error: function () {
            alert("Failed to load users");
          },
        });
      }

      function loadFreelancers() {
        $.ajax({
          url: "/api/admin/freelancers",
          type: "GET",
          success: function (freelancers) {
            const tbody = document.querySelector("#freelancersTable tbody");
            tbody.innerHTML = "";

            let totalFreelancers = freelancers.length;
            let activeFreelancers = freelancers.filter(
              (f) => f.isActive
            ).length;

            document.getElementById("totalFreelancers").textContent =
              totalFreelancers;
            document.getElementById("activeFreelancers").textContent =
              activeFreelancers;

            freelancers.forEach((freelancer) => {
              const row = document.createElement("tr");
              row.innerHTML = `
                <td>${freelancer.name}</td>
                <td>${freelancer.email}</td>
                <td>${freelancer.profileData?.location || "N/A"}</td>
                <td>${freelancer.profileData?.skills || "N/A"}</td>
                <td>$${freelancer.profileData?.hourlyRate || "N/A"}/hr</td>
                <td><span class="status-badge ${
                  freelancer.isActive ? "active" : "inactive"
                }">${freelancer.isActive ? "Active" : "Inactive"}</span></td>
                <td>
                  <button onclick="toggleUserStatus('${
                    freelancer._id
                  }', ${!freelancer.isActive})" class="action-btn ${
                freelancer.isActive ? "deactivate" : "activate"
              }">
                    ${freelancer.isActive ? "Deactivate" : "Activate"}
                  </button>
                  <button onclick="deleteUser('${
                    freelancer._id
                  }')" class="action-btn delete">Delete</button>
                </td>
              `;
              tbody.appendChild(row);
            });
          },
          error: function () {
            alert("Failed to load freelancers");
          },
        });
      }

      function loadStats() {
        $.ajax({
          url: "/api/admin/users",
          type: "GET",
          success: function (users) {
            const totalUsers = users.length;
            const freelancers = users.filter(
              (u) => u.role === "freelancer"
            ).length;
            const today = new Date().toDateString();
            const newUsersToday = users.filter(
              (u) => new Date(u.createdAt).toDateString() === today
            ).length;
            const freelancerPercentage =
              totalUsers > 0 ? Math.round((freelancers / totalUsers) * 100) : 0;

            document.getElementById("totalPlatformUsers").textContent =
              totalUsers;
            document.getElementById("newUsersToday").textContent =
              newUsersToday;
            document.getElementById("freelancerPercentage").textContent =
              freelancerPercentage + "%";
          },
        });
      }

      function toggleUserStatus(userId, newStatus) {
        $.ajax({
          url: `/api/admin/users/${userId}/status`,
          type: "PUT",
          contentType: "application/json",
          data: JSON.stringify({ isActive: newStatus }),
          success: function () {
            loadUsers();
            loadFreelancers();
            loadStats();
          },
          error: function () {
            alert("Failed to update user status");
          },
        });
      }

      function deleteUser(userId) {
        if (
          confirm(
            "Are you sure you want to delete this user? This action cannot be undone."
          )
        ) {
          $.ajax({
            url: `/api/admin/users/${userId}`,
            type: "DELETE",
            success: function () {
              loadUsers();
              loadFreelancers();
              loadStats();
            },
            error: function () {
              alert("Failed to delete user");
            },
          });
        }
      }

      function logout() {
        $.ajax({
          url: "/api/logout",
          type: "POST",
          success: function () {
            window.location.href = "/login.html";
          },
        });
      }

      // Initialize admin dashboard
      $(document).ready(function () {
        checkAdminAuth();
      });
    </script>
  </body>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: Arial, sans-serif;
      background: #f5f5f5;
      color: #333;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }

    .header {
      background: white;
      padding: 2rem;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header h1 {
      color: #333;
      font-size: 2rem;
    }

    .admin-info {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .logout-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 5px;
      cursor: pointer;
    }

    .tabs {
      display: flex;
      background: white;
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 2rem;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .tab-button {
      flex: 1;
      padding: 1rem 2rem;
      border: none;
      background: white;
      cursor: pointer;
      font-size: 1rem;
      transition: all 0.3s;
    }

    .tab-button.active {
      background: #667eea;
      color: white;
    }

    .tab-content {
      display: none;
      background: white;
      border-radius: 10px;
      padding: 2rem;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .tab-content.active {
      display: block;
    }

    .section-header {
      margin-bottom: 2rem;
    }

    .section-header h2 {
      margin-bottom: 1rem;
      color: #333;
    }

    .stats-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
    }

    .stat-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 1.5rem;
      border-radius: 10px;
      text-align: center;
    }

    .stat-card.large {
      padding: 2rem;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 0.5rem;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.9;
    }

    .table-container {
      overflow-x: auto;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 1rem;
    }

    th,
    td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid #eee;
    }

    th {
      background: #f8f9fa;
      font-weight: bold;
      color: #333;
    }

    .role-badge {
      padding: 0.25rem 0.5rem;
      border-radius: 15px;
      font-size: 0.8rem;
      font-weight: bold;
      text-transform: uppercase;
    }

    .role-badge.user {
      background: #e3f2fd;
      color: #1976d2;
    }

    .role-badge.freelancer {
      background: #fff3e0;
      color: #f57c00;
    }

    .role-badge.admin {
      background: #ffebee;
      color: #d32f2f;
    }

    .status-badge {
      padding: 0.25rem 0.5rem;
      border-radius: 15px;
      font-size: 0.8rem;
      font-weight: bold;
    }

    .status-badge.active {
      background: #e8f5e8;
      color: #2e7d32;
    }

    .status-badge.inactive {
      background: #ffebee;
      color: #d32f2f;
    }

    .action-btn {
      padding: 0.25rem 0.5rem;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-size: 0.8rem;
      margin-right: 0.5rem;
    }

    .action-btn.activate {
      background: #4caf50;
      color: white;
    }

    .action-btn.deactivate {
      background: #ff9800;
      color: white;
    }

    .action-btn.delete {
      background: #f44336;
      color: white;
    }

    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }

      .header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
      }

      .tabs {
        flex-direction: column;
      }

      .stats-cards {
        grid-template-columns: 1fr;
      }

      table {
        font-size: 0.8rem;
      }

      th,
      td {
        padding: 0.5rem;
      }
    }
  </style>
</html>
