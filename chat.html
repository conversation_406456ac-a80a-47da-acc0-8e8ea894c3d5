<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Chat - Freelancer Platform</title>
    <script
      src="https://code.jquery.com/jquery-3.4.1.js"
      integrity="sha256-WpOohJOqMqqyKL9FccASB9O0KwACQJpFTUBLTYOVvVU="
      crossorigin="anonymous"
    ></script>
  </head>
  <body>
    <div class="container">
      <!-- Navigation -->
      <nav class="navbar">
        <div class="nav-brand">
          <h2>FreelancerHub</h2>
        </div>
        <div class="nav-links">
          <a href="/">Home</a>
          <a href="/freelancers-browse.html">Browse Freelancers</a>
          <a href="/chat.html" class="active">Messages</a>
          <span id="userInfo">Loading...</span>
          <button onclick="logout()" class="logout-btn">Logout</button>
        </div>
      </nav>

      <div class="chat-container">
        <!-- Chat List -->
        <div class="chat-sidebar">
          <div class="sidebar-header">
            <h3>Messages</h3>
            <button onclick="refreshChats()" class="refresh-btn">🔄</button>
          </div>
          <div class="chat-list" id="chatList">
            <!-- Chat list will be loaded here -->
          </div>
        </div>

        <!-- Chat Area -->
        <div class="chat-main">
          <div id="noChatSelected" class="no-chat-selected">
            <div class="no-chat-content">
              <h3>💬 Select a conversation</h3>
              <p>Choose a chat from the sidebar to start messaging</p>
            </div>
          </div>

          <div id="chatArea" class="chat-area" style="display: none">
            <!-- Chat Header -->
            <div class="chat-header">
              <div class="chat-user-info">
                <img
                  id="chatUserAvatar"
                  src=""
                  alt="User Avatar"
                  class="chat-avatar"
                />
                <div class="chat-user-details">
                  <h4 id="chatUserName">Loading...</h4>
                  <span class="online-status">Online</span>
                </div>
              </div>
              <button onclick="closeChatArea()" class="close-chat-btn">
                ✕
              </button>
            </div>

            <!-- Messages Container -->
            <div class="messages-container" id="messagesContainer">
              <!-- Messages will be loaded here -->
            </div>

            <!-- Message Input -->
            <div class="message-input-container">
              <div class="message-input">
                <input
                  type="text"
                  id="messageInput"
                  placeholder="Type your message..."
                />
                <button onclick="sendMessage()" class="send-btn">Send</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      let currentUser = null;
      let currentChatId = null;
      let currentChat = null;

      // Check authentication
      function checkAuth() {
        $.ajax({
          url: "/api/auth/status",
          type: "GET",
          success: function (response) {
            if (response.authenticated) {
              currentUser = response.user;
              document.getElementById(
                "userInfo"
              ).textContent = `Welcome, ${currentUser.name}`;
              loadChats();

              // Check if specific chat ID is provided in URL
              const urlParams = new URLSearchParams(window.location.search);
              const chatId = urlParams.get("chatId");
              if (chatId) {
                loadChat(chatId);
              }
            } else {
              window.location.href = "/login.html";
            }
          },
          error: function () {
            window.location.href = "/login.html";
          },
        });
      }

      // Load all chats
      function loadChats() {
        $.ajax({
          url: "/api/chats",
          type: "GET",
          success: function (chats) {
            displayChatList(chats);
          },
          error: function () {
            console.error("Failed to load chats");
          },
        });
      }

      // Display chat list
      function displayChatList(chats) {
        const chatList = document.getElementById("chatList");

        if (chats.length === 0) {
          chatList.innerHTML =
            '<div class="no-chats">No conversations yet</div>';
          return;
        }

        chatList.innerHTML = chats
          .map((chat) => {
            const otherParticipant = chat.participants.find(
              (p) => p._id !== currentUser.id
            );
            const lastMessage = chat.lastMessage;

            return `
            <div class="chat-item ${
              chat._id === currentChatId ? "active" : ""
            }" 
                 onclick="loadChat('${chat._id}')">
              <img src="${
                otherParticipant?.profileData?.profileImage ||
                "https://via.placeholder.com/50"
              }" 
                   alt="${otherParticipant?.name}" class="chat-item-avatar" />
              <div class="chat-item-content">
                <div class="chat-item-header">
                  <h4>${otherParticipant?.name || "Unknown User"}</h4>
                  <span class="chat-time">${
                    lastMessage ? formatTime(lastMessage.timestamp) : ""
                  }</span>
                </div>
                <p class="last-message">${
                  lastMessage ? lastMessage.content : "No messages yet"
                }</p>
              </div>
            </div>
          `;
          })
          .join("");
      }

      // Load specific chat
      function loadChat(chatId) {
        currentChatId = chatId;

        $.ajax({
          url: `/api/chat/${chatId}`,
          type: "GET",
          success: function (chat) {
            currentChat = chat;
            displayChat(chat);
            loadChats(); // Refresh chat list to update active state
          },
          error: function () {
            alert("Failed to load chat");
          },
        });
      }

      // Display chat
      function displayChat(chat) {
        const otherParticipant = chat.participants.find(
          (p) => p._id !== currentUser.id
        );

        // Update chat header
        document.getElementById("chatUserAvatar").src =
          otherParticipant?.profileData?.profileImage ||
          "https://via.placeholder.com/50";
        document.getElementById("chatUserName").textContent =
          otherParticipant?.name || "Unknown User";

        // Display messages
        displayMessages(chat.messages);

        // Show chat area
        document.getElementById("noChatSelected").style.display = "none";
        document.getElementById("chatArea").style.display = "flex";
      }

      // Display messages
      function displayMessages(messages) {
        const container = document.getElementById("messagesContainer");

        container.innerHTML = messages
          .map((message) => {
            const isOwnMessage = message.sender._id === currentUser.id;
            return `
            <div class="message ${
              isOwnMessage ? "own-message" : "other-message"
            }">
              <div class="message-content">
                <p>${message.content}</p>
                <span class="message-time">${formatTime(
                  message.timestamp
                )}</span>
              </div>
            </div>
          `;
          })
          .join("");

        // Scroll to bottom
        container.scrollTop = container.scrollHeight;
      }

      // Send message
      function sendMessage() {
        const input = document.getElementById("messageInput");
        const content = input.value.trim();

        if (!content || !currentChatId) return;

        $.ajax({
          url: `/api/chat/${currentChatId}/message`,
          type: "POST",
          contentType: "application/json",
          data: JSON.stringify({ content }),
          success: function (response) {
            input.value = "";
            displayMessages(response.chat.messages);
            loadChats(); // Refresh chat list
          },
          error: function () {
            alert("Failed to send message");
          },
        });
      }

      // Format time
      function formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diffInHours = (now - date) / (1000 * 60 * 60);

        if (diffInHours < 24) {
          return date.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          });
        } else {
          return date.toLocaleDateString();
        }
      }

      // Close chat area
      function closeChatArea() {
        document.getElementById("noChatSelected").style.display = "flex";
        document.getElementById("chatArea").style.display = "none";
        currentChatId = null;
        currentChat = null;
        loadChats(); // Refresh to remove active state
      }

      // Refresh chats
      function refreshChats() {
        loadChats();
      }

      // Logout
      function logout() {
        $.ajax({
          url: "/api/logout",
          type: "POST",
          success: function () {
            window.location.href = "/login.html";
          },
        });
      }

      // Initialize
      $(document).ready(function () {
        checkAuth();

        // Send message on Enter key
        document
          .getElementById("messageInput")
          .addEventListener("keypress", function (e) {
            if (e.key === "Enter") {
              sendMessage();
            }
          });
      });
    </script>
  </body>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: "Arial", sans-serif;
      background: #f8f9fa;
      color: #333;
      height: 100vh;
      overflow: hidden;
    }

    .container {
      height: 100vh;
      display: flex;
      flex-direction: column;
    }

    /* Navigation */
    .navbar {
      background: white;
      padding: 1rem 2rem;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-shrink: 0;
    }

    .nav-brand h2 {
      color: #667eea;
      font-size: 1.5rem;
    }

    .nav-links {
      display: flex;
      align-items: center;
      gap: 2rem;
    }

    .nav-links a {
      text-decoration: none;
      color: #333;
      font-weight: 500;
      transition: color 0.3s;
      padding: 0.5rem 1rem;
      border-radius: 5px;
    }

    .nav-links a:hover,
    .nav-links a.active {
      color: #667eea;
      background: #f0f4ff;
    }

    .logout-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 5px;
      cursor: pointer;
    }

    /* Chat Container */
    .chat-container {
      display: flex;
      flex: 1;
      overflow: hidden;
    }

    /* Chat Sidebar */
    .chat-sidebar {
      width: 350px;
      background: white;
      border-right: 1px solid #eee;
      display: flex;
      flex-direction: column;
    }

    .sidebar-header {
      padding: 1.5rem;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .sidebar-header h3 {
      color: #333;
      font-size: 1.3rem;
    }

    .refresh-btn {
      background: none;
      border: none;
      font-size: 1.2rem;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 5px;
      transition: background 0.3s;
    }

    .refresh-btn:hover {
      background: #f0f4ff;
    }

    .chat-list {
      flex: 1;
      overflow-y: auto;
    }

    .chat-item {
      display: flex;
      align-items: center;
      padding: 1rem 1.5rem;
      cursor: pointer;
      transition: background 0.3s;
      border-bottom: 1px solid #f8f9fa;
    }

    .chat-item:hover {
      background: #f8f9fa;
    }

    .chat-item.active {
      background: #e3f2fd;
      border-right: 3px solid #667eea;
    }

    .chat-item-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 1rem;
    }

    .chat-item-content {
      flex: 1;
      min-width: 0;
    }

    .chat-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.3rem;
    }

    .chat-item-header h4 {
      font-size: 1rem;
      color: #333;
      margin: 0;
    }

    .chat-time {
      font-size: 0.8rem;
      color: #666;
    }

    .last-message {
      color: #666;
      font-size: 0.9rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0;
    }

    .no-chats {
      text-align: center;
      padding: 2rem;
      color: #666;
    }

    /* Chat Main Area */
    .chat-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: white;
    }

    .no-chat-selected {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f9fa;
    }

    .no-chat-content {
      text-align: center;
      color: #666;
    }

    .no-chat-content h3 {
      margin-bottom: 1rem;
      font-size: 1.5rem;
    }

    .chat-area {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    /* Chat Header */
    .chat-header {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: white;
    }

    .chat-user-info {
      display: flex;
      align-items: center;
    }

    .chat-avatar {
      width: 45px;
      height: 45px;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 1rem;
    }

    .chat-user-details h4 {
      margin: 0 0 0.2rem 0;
      color: #333;
    }

    .online-status {
      font-size: 0.8rem;
      color: #28a745;
    }

    .close-chat-btn {
      background: none;
      border: none;
      font-size: 1.2rem;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 5px;
      color: #666;
      transition: all 0.3s;
    }

    .close-chat-btn:hover {
      background: #f0f0f0;
      color: #333;
    }

    /* Messages Container */
    .messages-container {
      flex: 1;
      overflow-y: auto;
      padding: 1rem;
      background: #f8f9fa;
    }

    .message {
      margin-bottom: 1rem;
      display: flex;
    }

    .message.own-message {
      justify-content: flex-end;
    }

    .message.other-message {
      justify-content: flex-start;
    }

    .message-content {
      max-width: 70%;
      padding: 0.75rem 1rem;
      border-radius: 18px;
      position: relative;
    }

    .own-message .message-content {
      background: #667eea;
      color: white;
      border-bottom-right-radius: 5px;
    }

    .other-message .message-content {
      background: white;
      color: #333;
      border-bottom-left-radius: 5px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .message-content p {
      margin: 0 0 0.3rem 0;
      word-wrap: break-word;
    }

    .message-time {
      font-size: 0.7rem;
      opacity: 0.7;
    }

    /* Message Input */
    .message-input-container {
      padding: 1rem 1.5rem;
      border-top: 1px solid #eee;
      background: white;
    }

    .message-input {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    .message-input input {
      flex: 1;
      padding: 0.75rem 1rem;
      border: 2px solid #ddd;
      border-radius: 25px;
      font-size: 1rem;
      outline: none;
      transition: border-color 0.3s;
    }

    .message-input input:focus {
      border-color: #667eea;
    }

    .send-btn {
      padding: 0.75rem 1.5rem;
      background: #667eea;
      color: white;
      border: none;
      border-radius: 25px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s;
    }

    .send-btn:hover {
      background: #5a6fd8;
      transform: translateY(-1px);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .chat-sidebar {
        width: 100%;
        position: absolute;
        z-index: 1000;
        height: calc(100vh - 70px);
      }

      .chat-main {
        width: 100%;
      }

      .nav-links {
        flex-direction: column;
        gap: 0.5rem;
      }

      .message-content {
        max-width: 85%;
      }
    }

    /* Scrollbar Styling */
    .chat-list::-webkit-scrollbar,
    .messages-container::-webkit-scrollbar {
      width: 6px;
    }

    .chat-list::-webkit-scrollbar-track,
    .messages-container::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    .chat-list::-webkit-scrollbar-thumb,
    .messages-container::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    .chat-list::-webkit-scrollbar-thumb:hover,
    .messages-container::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  </style>
</html>
