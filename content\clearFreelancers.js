const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect("mongodb://localhost:27017/freelancer_platform", {
    useNewUrlParser: true,
    useUnifiedTopology: true
})
.then(() => {
    console.log('MongoDB connected for clearing freelancer data');
})
.catch((error) => {
    console.error('MongoDB connection error:', error);
});

// Freelancer Schema (same as in seedData.js)
const freelancerSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    location: {
        type: String,
        required: true
    },
    job: {
        type: String,
        required: true
    },
    skills: {
        type: String,
        required: true
    },
    profileUrl: {
        type: String,
        required: true
    },
    imageUrl: {
        type: String,
        required: true
    },
    platform: {
        type: String,
        required: true,
        enum: ['fiverr', 'upwork', 'freelancer', 'other']
    },
    category: {
        type: String,
        required: true,
        enum: ['content_writer', 'video_editor', 'full_stack', 'graphic_designer', 'other']
    },
    rating: {
        type: Number,
        min: 0,
        max: 5,
        default: 0
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
});

const Freelancer = mongoose.model('Freelancer', freelancerSchema);

// Function to clear freelancer data
async function clearFreelancers() {
    try {
        // Count existing freelancers
        const count = await Freelancer.countDocuments();
        console.log(`Found ${count} freelancer profiles in database`);

        if (count === 0) {
            console.log('No freelancer profiles to remove');
            return;
        }

        // Clear all freelancer data
        const result = await Freelancer.deleteMany({});
        console.log(`✅ Successfully removed ${result.deletedCount} freelancer profiles`);
        
        // Verify deletion
        const remainingCount = await Freelancer.countDocuments();
        console.log(`Remaining freelancer profiles: ${remainingCount}`);
        
    } catch (error) {
        console.error('❌ Error clearing freelancer data:', error);
    } finally {
        mongoose.connection.close();
        console.log('\nDatabase connection closed');
    }
}

// Run the clear function
clearFreelancers();
