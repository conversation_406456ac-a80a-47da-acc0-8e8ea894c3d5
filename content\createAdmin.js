const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

// Connect to MongoDB
mongoose.connect("mongodb://localhost:27017/freelancer_platform", {
    useNewUrlParser: true,
    useUnifiedTopology: true
})
.then(() => {
    console.log('MongoDB connected for admin creation');
})
.catch((error) => {
    console.error('MongoDB connection error:', error);
});

// User Schema (same as in main app)
const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  password: {
    type: String,
    required: true,
    minlength: 6,
  },
  role: {
    type: String,
    required: true,
    enum: ["user", "freelancer", "admin"],
    default: "user",
  },
  profileData: {
    location: String,
    skills: String,
    experience: String,
    portfolio: String,
    hourlyRate: Number,
    availability: String,
    bio: String,
    profileImage: String,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

const User = mongoose.model('User', userSchema);

// Function to create admin user
async function createAdmin() {
    try {
        // Check if admin already exists
        const existingAdmin = await User.findOne({ role: 'admin' });
        if (existingAdmin) {
            console.log('Admin user already exists:', existingAdmin.email);
            return;
        }

        // Admin credentials
        const adminData = {
            name: 'Admin',
            email: '<EMAIL>',
            password: 'admin123',
            role: 'admin'
        };

        // Hash password
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(adminData.password, saltRounds);

        // Create admin user
        const admin = new User({
            name: adminData.name,
            email: adminData.email,
            password: hashedPassword,
            role: adminData.role,
            profileData: {}
        });

        await admin.save();

        console.log('✅ Admin user created successfully!');
        console.log('📧 Email:', adminData.email);
        console.log('🔑 Password:', adminData.password);
        console.log('🌐 Access admin dashboard at: http://localhost:3000/admin.html');
        console.log('\n⚠️  Please change the admin password after first login!');

    } catch (error) {
        console.error('❌ Error creating admin user:', error);
    } finally {
        mongoose.connection.close();
        console.log('\nDatabase connection closed');
    }
}

// Run the admin creation
createAdmin();
