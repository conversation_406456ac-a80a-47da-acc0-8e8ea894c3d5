const express = require("express");
const mongoose = require("mongoose");
const path = require("path");
const bcrypt = require("bcrypt");
const session = require("express-session");
const MongoStore = require("connect-mongo");

const app = express();
const PORT = process.env.PORT || 3000;

// MongoDB Connection
mongoose
  .connect("mongodb://localhost:27017/freelancer_platform", {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  })
  .then(() => {
    console.log("MongoDB connected successfully");
  })
  .catch((error) => {
    console.error("MongoDB connection error:", error);
  });

// User Schema for Authentication
const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  password: {
    type: String,
    required: true,
    minlength: 6,
  },
  role: {
    type: String,
    required: true,
    enum: ["user", "freelancer", "admin"],
    default: "user",
  },
  // Additional fields for freelancers
  profileData: {
    location: String,
    skills: String,
    experience: String,
    portfolio: String,
    hourlyRate: Number,
    availability: String,
    bio: String,
    profileImage: String,
    completedProjects: [
      {
        title: String,
        description: String,
        client: String,
        completedDate: Date,
        rating: Number,
        testimonial: String,
        projectImage: String,
      },
    ],
    socialLinks: {
      website: String,
      linkedin: String,
      github: String,
      behance: String,
    },
  },
  // Social features
  followers: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],
  following: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],
  isActive: {
    type: Boolean,
    default: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

// Freelancer Schema for storing freelancer profiles
const freelancerSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  location: {
    type: String,
    required: true,
  },
  job: {
    type: String,
    required: true,
  },
  skills: {
    type: String,
    required: true,
  },
  profileUrl: {
    type: String,
    required: true,
  },
  imageUrl: {
    type: String,
    required: true,
  },
  platform: {
    type: String,
    required: true,
    enum: ["fiverr", "upwork", "freelancer", "other"],
  },
  category: {
    type: String,
    required: true,
    enum: [
      "content_writer",
      "video_editor",
      "full_stack",
      "graphic_designer",
      "other",
    ],
  },
  rating: {
    type: Number,
    min: 0,
    max: 5,
    default: 0,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

// Chat Schema for freelancer communication
const chatSchema = new mongoose.Schema({
  participants: [
    { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
  ],
  messages: [
    {
      sender: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
      },
      content: { type: String, required: true },
      timestamp: { type: Date, default: Date.now },
      read: { type: Boolean, default: false },
    },
  ],
  lastMessage: {
    content: String,
    timestamp: Date,
    sender: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

const User = mongoose.model("User", userSchema);
const Freelancer = mongoose.model("Freelancer", freelancerSchema);
const Chat = mongoose.model("Chat", chatSchema);

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, "..")));

// Session configuration
app.use(
  session({
    secret: "freelancer_platform_secret_key",
    resave: false,
    saveUninitialized: false,
    store: MongoStore.create({
      mongoUrl: "mongodb://localhost:27017/freelancer_platform",
    }),
    cookie: {
      secure: false, // Set to true in production with HTTPS
      httpOnly: true,
      maxAge: 1000 * 60 * 60 * 24, // 24 hours
    },
  })
);

// Routes

// Home route - serve the main page
app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, "..", "home.html"));
});

// API route to get all freelancers
app.get("/api/freelancers", async (req, res) => {
  try {
    const freelancers = await Freelancer.find().sort({ createdAt: -1 });
    res.json(freelancers);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch freelancers" });
  }
});

// API route to get freelancers by category
app.get("/api/freelancers/:category", async (req, res) => {
  try {
    const { category } = req.params;
    const freelancers = await Freelancer.find({ category }).sort({
      createdAt: -1,
    });
    res.json(freelancers);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch freelancers" });
  }
});

// User Registration
app.post("/api/signup", async (req, res) => {
  try {
    const { name, email, password, role, profileData } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res
        .status(400)
        .json({ error: "User already exists with this email" });
    }

    // Hash password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create new user
    const newUser = new User({
      name,
      email,
      password: hashedPassword,
      role: role || "user",
      profileData: profileData || {},
    });

    await newUser.save();

    // Create session
    req.session.userId = newUser._id;
    req.session.userName = newUser.name;
    req.session.userRole = newUser.role;

    res.status(201).json({
      message: "User registered successfully",
      user: {
        id: newUser._id,
        name: newUser.name,
        email: newUser.email,
        role: newUser.role,
      },
    });
  } catch (error) {
    console.error("Signup error:", error);
    res.status(500).json({ error: "Registration failed" });
  }
});

// User Login
app.post("/api/login", async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(400).json({ error: "Invalid email or password" });
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(400).json({ error: "Invalid email or password" });
    }

    // Create session
    req.session.userId = user._id;
    req.session.userName = user.name;
    req.session.userRole = user.role;

    res.json({
      message: "Login successful",
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ error: "Login failed" });
  }
});

// User Logout
app.post("/api/logout", (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      return res.status(500).json({ error: "Logout failed" });
    }
    res.json({ message: "Logout successful" });
  });
});

// Check authentication status
app.get("/api/auth/status", (req, res) => {
  if (req.session.userId) {
    res.json({
      authenticated: true,
      user: {
        id: req.session.userId,
        name: req.session.userName,
        role: req.session.userRole,
      },
    });
  } else {
    res.json({ authenticated: false });
  }
});

// Admin middleware
const requireAdmin = (req, res, next) => {
  if (!req.session.userId || req.session.userRole !== "admin") {
    return res.status(403).json({ error: "Admin access required" });
  }
  next();
};

// Admin routes
app.get("/api/admin/users", requireAdmin, async (req, res) => {
  try {
    const users = await User.find({}, "-password").sort({ createdAt: -1 });
    res.json(users);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch users" });
  }
});

app.get("/api/admin/freelancers", requireAdmin, async (req, res) => {
  try {
    const freelancers = await User.find(
      { role: "freelancer" },
      "-password"
    ).sort({ createdAt: -1 });
    res.json(freelancers);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch freelancers" });
  }
});

app.put("/api/admin/users/:id/status", requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    const user = await User.findByIdAndUpdate(
      id,
      { isActive },
      { new: true, select: "-password" }
    );

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    res.json({ message: "User status updated", user });
  } catch (error) {
    res.status(500).json({ error: "Failed to update user status" });
  }
});

app.delete("/api/admin/users/:id", requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const user = await User.findByIdAndDelete(id);

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    res.json({ message: "User deleted successfully" });
  } catch (error) {
    res.status(500).json({ error: "Failed to delete user" });
  }
});

// Freelancer Profile Routes
app.get("/api/freelancer/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const freelancer = await User.findById(id)
      .select("-password")
      .populate("followers", "name profileData.profileImage")
      .populate("following", "name profileData.profileImage");

    if (!freelancer || freelancer.role !== "freelancer") {
      return res.status(404).json({ error: "Freelancer not found" });
    }

    res.json(freelancer);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch freelancer profile" });
  }
});

// Get all freelancers for browsing
app.get("/api/freelancers/browse", async (req, res) => {
  try {
    const freelancers = await User.find({ role: "freelancer", isActive: true })
      .select("-password")
      .sort({ createdAt: -1 });
    res.json(freelancers);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch freelancers" });
  }
});

// Follow/Unfollow system
app.post("/api/freelancer/:id/follow", async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { id } = req.params;
    const currentUserId = req.session.userId;

    if (id === currentUserId) {
      return res.status(400).json({ error: "Cannot follow yourself" });
    }

    const targetUser = await User.findById(id);
    const currentUser = await User.findById(currentUserId);

    if (!targetUser || !currentUser) {
      return res.status(404).json({ error: "User not found" });
    }

    const isFollowing = currentUser.following.includes(id);

    if (isFollowing) {
      // Unfollow
      currentUser.following.pull(id);
      targetUser.followers.pull(currentUserId);
    } else {
      // Follow
      currentUser.following.push(id);
      targetUser.followers.push(currentUserId);
    }

    await currentUser.save();
    await targetUser.save();

    res.json({
      message: isFollowing
        ? "Unfollowed successfully"
        : "Followed successfully",
      isFollowing: !isFollowing,
    });
  } catch (error) {
    res.status(500).json({ error: "Failed to update follow status" });
  }
});

// Update freelancer profile
app.put("/api/freelancer/profile", async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const userId = req.session.userId;
    const updateData = req.body;

    const user = await User.findByIdAndUpdate(
      userId,
      {
        name: updateData.name,
        profileData: updateData.profileData,
      },
      { new: true, select: "-password" }
    );

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    res.json({ message: "Profile updated successfully", user });
  } catch (error) {
    res.status(500).json({ error: "Failed to update profile" });
  }
});

// Chat Routes
app.get("/api/chats", async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const chats = await Chat.find({ participants: req.session.userId })
      .populate("participants", "name profileData.profileImage")
      .populate("lastMessage.sender", "name")
      .sort({ updatedAt: -1 });

    res.json(chats);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch chats" });
  }
});

app.get("/api/chat/:chatId", async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { chatId } = req.params;
    const chat = await Chat.findById(chatId)
      .populate("participants", "name profileData.profileImage")
      .populate("messages.sender", "name profileData.profileImage");

    if (
      !chat ||
      !chat.participants.some((p) => p._id.toString() === req.session.userId)
    ) {
      return res.status(404).json({ error: "Chat not found" });
    }

    res.json(chat);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch chat" });
  }
});

app.post("/api/chat", async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { recipientId } = req.body;
    const currentUserId = req.session.userId;

    if (recipientId === currentUserId) {
      return res.status(400).json({ error: "Cannot chat with yourself" });
    }

    // Check if chat already exists
    let chat = await Chat.findOne({
      participants: { $all: [currentUserId, recipientId] },
    }).populate("participants", "name profileData.profileImage");

    if (!chat) {
      // Create new chat
      chat = new Chat({
        participants: [currentUserId, recipientId],
        messages: [],
      });
      await chat.save();
      await chat.populate("participants", "name profileData.profileImage");
    }

    res.json(chat);
  } catch (error) {
    res.status(500).json({ error: "Failed to create chat" });
  }
});

app.post("/api/chat/:chatId/message", async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { chatId } = req.params;
    const { content } = req.body;

    const chat = await Chat.findById(chatId);
    if (!chat || !chat.participants.includes(req.session.userId)) {
      return res.status(404).json({ error: "Chat not found" });
    }

    const newMessage = {
      sender: req.session.userId,
      content: content,
      timestamp: new Date(),
    };

    chat.messages.push(newMessage);
    chat.lastMessage = {
      content: content,
      timestamp: new Date(),
      sender: req.session.userId,
    };
    chat.updatedAt = new Date();

    await chat.save();
    await chat.populate("messages.sender", "name profileData.profileImage");

    res.json({ message: "Message sent successfully", chat });
  } catch (error) {
    res.status(500).json({ error: "Failed to send message" });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log("Freelancer Platform is ready!");
});

module.exports = { app, User, Freelancer, Chat };
