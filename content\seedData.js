const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect("mongodb://localhost:27017/freelancer_platform", {
    useNewUrlParser: true,
    useUnifiedTopology: true
})
.then(() => {
    console.log('MongoDB connected for seeding');
})
.catch((error) => {
    console.error('MongoDB connection error:', error);
});

// Freelancer Schema (same as in main app)
const freelancerSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    location: {
        type: String,
        required: true
    },
    job: {
        type: String,
        required: true
    },
    skills: {
        type: String,
        required: true
    },
    profileUrl: {
        type: String,
        required: true
    },
    imageUrl: {
        type: String,
        required: true
    },
    platform: {
        type: String,
        required: true,
        enum: ['fiverr', 'upwork', 'freelancer', 'other']
    },
    category: {
        type: String,
        required: true,
        enum: ['content_writer', 'video_editor', 'full_stack', 'graphic_designer', 'other']
    },
    rating: {
        type: Number,
        min: 0,
        max: 5,
        default: 0
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
});

const Freelancer = mongoose.model('Freelancer', freelancerSchema);

// Sample freelancer data based on your HTML
const freelancersData = [
    // Content Writers
    {
        name: "Parth",
        location: "Delhi, India",
        job: "Content Writer",
        skills: "Blog Writer, Proofreader",
        profileUrl: "https://www.fiverr.com/parthmunjani?source=gig_cards&referrer_gig_slug=write-an-200-to-1500-words-compelling-about-us-page&ref_ctx_id=a637c0daa6b949cc9041645763cfe338&imp_id=623c1f3e-2639-402a-8a09-eef0da18be03",
        imageUrl: "content/50a21ca3-f114-4ded-a71b-5e0ff698e1c9.webp",
        platform: "fiverr",
        category: "content_writer",
        rating: 4.8
    },
    {
        name: "Lita L",
        location: "London, UK",
        job: "Website Content Writer",
        skills: "Photoshop, Illustrator, InDesign",
        profileUrl: "https://www.fiverr.com/litaluuse/write-persuasive-website-content?context_referrer=search_gigs&source=top-bar&ref_ctx_id=a637c0daa6b949cc9041645763cfe338&pckg_id=1&pos=5&context_type=auto&funnel=a637c0daa6b949cc9041645763cfe338&imp_id=88dfe8a3-2d24-4169-8680-6b548b552e5f",
        imageUrl: "content/2.webp",
        platform: "fiverr",
        category: "content_writer",
        rating: 4.9
    },
    {
        name: "Ahmad",
        location: "Pune, India",
        job: "SEO Content Writer",
        skills: "Blog Writer",
        profileUrl: "https://www.fiverr.com/ahmad_2802/write-seo-website-content-article-copywriting-and-blog-post?context_referrer=gig_page&source=similar_gigs&ref_ctx_id=ff1f6979b67047cf9b8ca61928e6e76b&context=recommendation&pckg_id=1&pos=1&mod=ff&context_alg=t2g_dfm&imp_id=cfe9f5f4-0116-4971-bd0d-3a6dacce8732",
        imageUrl: "content/3.webp",
        platform: "fiverr",
        category: "content_writer",
        rating: 4.7
    },
    {
        name: "Victor",
        location: "Mumbai, India",
        job: "Graphic Designer",
        skills: "Website Content Writer",
        profileUrl: "https://www.fiverr.com/victorraffalli/write-funny-jokes-routines-speeches-and-scripts?context_referrer=search_gigs&source=drop_down_filters&ref_ctx_id=350f4b2fbbc24d4490cc5163596c3ede&pckg_id=1&pos=11&context_type=auto&funnel=350f4b2fbbc24d4490cc5163596c3ede&ref=leaf_category%3A107&imp_id=5f4c8e68-e1f9-45d8-941c-a5f8d2a2438e",
        imageUrl: "content/4.webp",
        platform: "fiverr",
        category: "content_writer",
        rating: 4.6
    },
    
    // Video Editors
    {
        name: "Juan",
        location: "Colombia",
        job: "Video Editor",
        skills: "Blog Writer, Proofreader",
        profileUrl: "https://www.fiverr.com/juan_depaz?source=gig_cards&referrer_gig_slug=do-professional-video-editing-and-motion-graphics&ref_ctx_id=00c2d49660e44d7990f093b21581ef40&imp_id=5ca8e8ce-4b55-41cd-a040-3061e8e25411",
        imageUrl: "images/11.webp",
        platform: "fiverr",
        category: "video_editor",
        rating: 4.9
    },
    {
        name: "Raj S",
        location: "India",
        job: "Video Editor",
        skills: "Photoshop, Illustrator, InDesign",
        profileUrl: "https://www.fiverr.com/r73098?source=gig_cards&referrer_gig_slug=do-professional-video-editing-within-24-hours-f34b&ref_ctx_id=4bbae2a6b5e7478fb899d37146be1d8f&imp_id=9ec3924a-d6ab-48ba-8664-00fb1c34b4ff",
        imageUrl: "images/12.jpeg",
        platform: "fiverr",
        category: "video_editor",
        rating: 4.8
    },
    {
        name: "Joe",
        location: "Rio De Janeiro, Brazil",
        job: "YT Video Editor",
        skills: "Premiere Pro",
        profileUrl: "https://www.fiverr.com/flubberfish?source=gig_cards&referrer_gig_slug=do-any-video-editing-or-after-effects-work&ref_ctx_id=4bbae2a6b5e7478fb899d37146be1d8f&imp_id=161db4de-4ab0-4953-b1a5-0208b709834e",
        imageUrl: "images/13.webp",
        platform: "fiverr",
        category: "video_editor",
        rating: 4.7
    },
    {
        name: "Idrees Younas",
        location: "Mumbai, India",
        job: "Graphic Designer",
        skills: "Animations",
        profileUrl: "https://www.fiverr.com/techxperta/do-professional-video-editing-within-24-hours?context_referrer=search_gigs_with_recommendations_row_1&source=top-bar&ref_ctx_id=4bbae2a6b5e7478fb899d37146be1d8f&pckg_id=1&pos=8&context_type=auto&funnel=4bbae2a6b5e7478fb899d37146be1d8f&seller_online=true&imp_id=c4b37015-3388-485f-8e49-0a6d173647de",
        imageUrl: "images/14.webp",
        platform: "fiverr",
        category: "video_editor",
        rating: 4.8
    },
    
    // Full Stack Developers
    {
        name: "Dev Muneeb",
        location: "Sri Lanka",
        job: "Full Stack Developer",
        skills: "React.js, React Native, Mobile App Development",
        profileUrl: "https://www.fiverr.com/devwith_muneeb/expert-full-stack-developer-mobile-app-web-app-developer-react-js-react-native?context_referrer=search_gigs&source=top-bar&ref_ctx_id=a723903053a2450ab1dd81a1660b7ebc&pckg_id=1&pos=1&context_type=auto&funnel=a723903053a2450ab1dd81a1660b7ebc&fiverr_choice=true&imp_id=27965936-c366-416e-a13d-90d8d5277f63",
        imageUrl: "images/21.webp",
        platform: "fiverr",
        category: "full_stack",
        rating: 4.9
    },
    {
        name: "James Kay",
        location: "United States",
        job: "Full Stack Developer",
        skills: "Front-end and Back End Developer",
        profileUrl: "https://www.fiverr.com/jamedeveloper?source=gig_cards&referrer_gig_slug=convert-psd-to-html-xd-to-html-bootstrap-google-material-design-responsive&ref_ctx_id=0a99b3bf4bbe420f95b2aad254e9e8f5&imp_id=a91c8817-05e9-44ac-be42-5765bf44727f",
        imageUrl: "images/23.webp",
        platform: "fiverr",
        category: "full_stack",
        rating: 4.8
    },
    {
        name: "Victor",
        location: "Ukraine",
        job: "Full Stack Developer",
        skills: "Front-end and Back End Developer",
        profileUrl: "https://www.fiverr.com/victor_ydc/be-full-stack-web-developer-software-developer?context_referrer=search_gigs&source=top-bar&ref_ctx_id=0a99b3bf4bbe420f95b2aad254e9e8f5&pckg_id=1&pos=17&context_type=auto&funnel=0a99b3bf4bbe420f95b2aad254e9e8f5&imp_id=5abfaad6-611e-464c-9e95-aaa464464250",
        imageUrl: "images/24.jpeg",
        platform: "fiverr",
        category: "full_stack",
        rating: 4.7
    }
];

// Function to seed the database
async function seedDatabase() {
    try {
        // Clear existing data
        await Freelancer.deleteMany({});
        console.log('Cleared existing freelancer data');

        // Insert new data
        const result = await Freelancer.insertMany(freelancersData);
        console.log(`Successfully seeded ${result.length} freelancers`);
        
        // Display seeded data
        console.log('\nSeeded freelancers:');
        result.forEach((freelancer, index) => {
            console.log(`${index + 1}. ${freelancer.name} - ${freelancer.job} (${freelancer.platform})`);
        });
        
    } catch (error) {
        console.error('Error seeding database:', error);
    } finally {
        mongoose.connection.close();
        console.log('\nDatabase connection closed');
    }
}

// Run the seeder
seedDatabase();
