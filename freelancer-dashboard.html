<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Freelancer Dashboard - Complete Your Profile</title>
    <script
      src="https://code.jquery.com/jquery-3.4.1.js"
      integrity="sha256-WpOohJOqMqqyKL9FccASB9O0KwACQJpFTUBLTYOVvVU="
      crossorigin="anonymous"
    ></script>
  </head>
  <body>
    <div class="container">
      <!-- Navigation -->
      <nav class="navbar">
        <div class="nav-brand">
          <h2>FreelancerHub</h2>
        </div>
        <div class="nav-links">
          <a href="/">Home</a>
          <a href="/freelancers-browse.html">Browse Freelancers</a>
          <a href="/chat.html">Messages</a>
          <span id="userInfo">Loading...</span>
          <button onclick="logout()" class="logout-btn">Logout</button>
        </div>
      </nav>

      <!-- Welcome Header -->
      <div class="welcome-header">
        <h1>Welcome to Your Freelancer Dashboard! 🎉</h1>
        <p>
          Complete your profile to start connecting with clients and other
          freelancers
        </p>
      </div>

      <!-- Profile Completion Progress -->
      <div class="progress-section">
        <h3>Profile Completion</h3>
        <div class="progress-bar">
          <div class="progress-fill" id="progressFill"></div>
        </div>
        <span class="progress-text" id="progressText">0% Complete</span>
      </div>

      <!-- Profile Setup Form -->
      <div class="profile-form">
        <!-- Basic Information -->
        <div class="form-section">
          <h3>📋 Basic Information</h3>
          <div class="form-row">
            <div class="form-group">
              <label for="fullName">Full Name *</label>
              <input
                type="text"
                id="fullName"
                placeholder="Your full name"
                required
              />
            </div>
            <div class="form-group">
              <label for="location">Location *</label>
              <input
                type="text"
                id="location"
                placeholder="City, Country"
                required
              />
            </div>
          </div>

          <div class="form-group">
            <label for="bio">Professional Bio *</label>
            <textarea
              id="bio"
              placeholder="Tell clients about your experience, skills, and what makes you unique..."
              rows="4"
              required
            ></textarea>
          </div>
        </div>

        <!-- Professional Details -->
        <div class="form-section">
          <h3>💼 Professional Details</h3>
          <div class="form-row">
            <div class="form-group">
              <label for="skills">Skills & Expertise *</label>
              <input
                type="text"
                id="skills"
                placeholder="e.g., Web Development, Graphic Design, Content Writing"
                required
              />
              <small>Separate skills with commas</small>
            </div>
            <div class="form-group">
              <label for="experience">Experience Level</label>
              <select id="experience">
                <option value="">Select experience</option>
                <option value="beginner">Beginner (0-1 years)</option>
                <option value="intermediate">Intermediate (2-4 years)</option>
                <option value="expert">Expert (5+ years)</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="hourlyRate">Hourly Rate (USD)</label>
              <input type="number" id="hourlyRate" placeholder="25" min="1" />
            </div>
            <div class="form-group">
              <label for="availability">Availability</label>
              <select id="availability">
                <option value="">Select availability</option>
                <option value="full-time">Full-time (40+ hours/week)</option>
                <option value="part-time">Part-time (20-40 hours/week)</option>
                <option value="project-based">Project-based</option>
                <option value="weekends">Weekends only</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Portfolio & Projects -->
        <div class="form-section">
          <h3>🎨 Portfolio & Completed Projects</h3>
          <div id="projectsContainer">
            <div class="project-item">
              <h4>Project #1</h4>
              <div class="form-row">
                <div class="form-group">
                  <label>Project Title</label>
                  <input
                    type="text"
                    class="project-title"
                    placeholder="Project name"
                  />
                </div>
                <div class="form-group">
                  <label>Client Name</label>
                  <input
                    type="text"
                    class="project-client"
                    placeholder="Client or company name"
                  />
                </div>
              </div>
              <div class="form-group">
                <label>Project Description</label>
                <textarea
                  class="project-description"
                  placeholder="Describe what you did for this project..."
                  rows="3"
                ></textarea>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>Completion Date</label>
                  <input type="date" class="project-date" />
                </div>
                <div class="form-group">
                  <label>Project Rating (1-5)</label>
                  <select class="project-rating">
                    <option value="">Select rating</option>
                    <option value="5">⭐⭐⭐⭐⭐ (5 stars)</option>
                    <option value="4">⭐⭐⭐⭐ (4 stars)</option>
                    <option value="3">⭐⭐⭐ (3 stars)</option>
                    <option value="2">⭐⭐ (2 stars)</option>
                    <option value="1">⭐ (1 star)</option>
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label>Client Testimonial (Optional)</label>
                <textarea
                  class="project-testimonial"
                  placeholder="What did the client say about your work?"
                  rows="2"
                ></textarea>
              </div>
            </div>
          </div>

          <button type="button" onclick="addProject()" class="add-project-btn">
            + Add Another Project
          </button>
        </div>

        <!-- Social Links -->
        <div class="form-section">
          <h3>🔗 Social & Professional Links</h3>
          <div class="form-row">
            <div class="form-group">
              <label for="website">Personal Website</label>
              <input
                type="url"
                id="website"
                placeholder="https://yourwebsite.com"
              />
            </div>
            <div class="form-group">
              <label for="linkedin">LinkedIn Profile</label>
              <input
                type="url"
                id="linkedin"
                placeholder="https://linkedin.com/in/yourprofile"
              />
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="github">GitHub Profile</label>
              <input
                type="url"
                id="github"
                placeholder="https://github.com/yourusername"
              />
            </div>
            <div class="form-group">
              <label for="behance">Behance/Portfolio</label>
              <input
                type="url"
                id="behance"
                placeholder="https://behance.net/yourprofile"
              />
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-actions">
          <button type="button" onclick="saveProfile()" class="save-btn">
            💾 Save Profile
          </button>
          <button type="button" onclick="previewProfile()" class="preview-btn">
            👁️ Preview Profile
          </button>
          <button type="button" onclick="skipForNow()" class="skip-btn">
            ⏭️ Skip for Now
          </button>
        </div>
      </div>
    </div>

    <script>
      let currentUser = null;
      let projectCount = 1;

      // Check authentication and load user data
      function checkAuth() {
        $.ajax({
          url: "/api/auth/status",
          type: "GET",
          success: function (response) {
            if (response.authenticated) {
              currentUser = response.user;
              document.getElementById(
                "userInfo"
              ).textContent = `Welcome, ${currentUser.name}`;

              // Check if user is freelancer
              if (currentUser.role !== "freelancer") {
                alert("This page is only for freelancers");
                window.location.href = "/";
                return;
              }

              loadUserProfile();
            } else {
              window.location.href = "/login.html";
            }
          },
          error: function () {
            window.location.href = "/login.html";
          },
        });
      }

      // Load existing user profile data
      function loadUserProfile() {
        $.ajax({
          url: `/api/freelancer/${currentUser.id}`,
          type: "GET",
          success: function (user) {
            populateForm(user);
            updateProgress();
          },
          error: function () {
            console.log("No existing profile data found");
            updateProgress();
          },
        });
      }

      // Populate form with existing data
      function populateForm(user) {
        document.getElementById("fullName").value = user.name || "";

        if (user.profileData) {
          document.getElementById("location").value =
            user.profileData.location || "";
          document.getElementById("bio").value = user.profileData.bio || "";
          document.getElementById("skills").value =
            user.profileData.skills || "";
          document.getElementById("experience").value =
            user.profileData.experience || "";
          document.getElementById("hourlyRate").value =
            user.profileData.hourlyRate || "";
          document.getElementById("availability").value =
            user.profileData.availability || "";

          // Social links
          if (user.profileData.socialLinks) {
            document.getElementById("website").value =
              user.profileData.socialLinks.website || "";
            document.getElementById("linkedin").value =
              user.profileData.socialLinks.linkedin || "";
            document.getElementById("github").value =
              user.profileData.socialLinks.github || "";
            document.getElementById("behance").value =
              user.profileData.socialLinks.behance || "";
          }

          // Projects
          if (
            user.profileData.completedProjects &&
            user.profileData.completedProjects.length > 0
          ) {
            populateProjects(user.profileData.completedProjects);
          }
        }
      }

      // Populate projects
      function populateProjects(projects) {
        const container = document.getElementById("projectsContainer");
        container.innerHTML = "";

        projects.forEach((project, index) => {
          addProjectItem(index + 1, project);
        });

        projectCount = projects.length;
      }

      // Add new project
      function addProject() {
        projectCount++;
        addProjectItem(projectCount);
        updateProgress();
      }

      // Add project item to DOM
      function addProjectItem(number, data = {}) {
        const container = document.getElementById("projectsContainer");
        const projectDiv = document.createElement("div");
        projectDiv.className = "project-item";
        projectDiv.innerHTML = `
          <h4>Project #${number} <button type="button" onclick="removeProject(this)" class="remove-project">×</button></h4>
          <div class="form-row">
            <div class="form-group">
              <label>Project Title</label>
              <input type="text" class="project-title" placeholder="Project name" value="${
                data.title || ""
              }" />
            </div>
            <div class="form-group">
              <label>Client Name</label>
              <input type="text" class="project-client" placeholder="Client or company name" value="${
                data.client || ""
              }" />
            </div>
          </div>
          <div class="form-group">
            <label>Project Description</label>
            <textarea class="project-description" placeholder="Describe what you did for this project..." rows="3">${
              data.description || ""
            }</textarea>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>Completion Date</label>
              <input type="date" class="project-date" value="${
                data.completedDate
                  ? new Date(data.completedDate).toISOString().split("T")[0]
                  : ""
              }" />
            </div>
            <div class="form-group">
              <label>Project Rating (1-5)</label>
              <select class="project-rating">
                <option value="">Select rating</option>
                <option value="5" ${
                  data.rating == 5 ? "selected" : ""
                }>⭐⭐⭐⭐⭐ (5 stars)</option>
                <option value="4" ${
                  data.rating == 4 ? "selected" : ""
                }>⭐⭐⭐⭐ (4 stars)</option>
                <option value="3" ${
                  data.rating == 3 ? "selected" : ""
                }>⭐⭐⭐ (3 stars)</option>
                <option value="2" ${
                  data.rating == 2 ? "selected" : ""
                }>⭐⭐ (2 stars)</option>
                <option value="1" ${
                  data.rating == 1 ? "selected" : ""
                }>⭐ (1 star)</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label>Client Testimonial (Optional)</label>
            <textarea class="project-testimonial" placeholder="What did the client say about your work?" rows="2">${
              data.testimonial || ""
            }</textarea>
          </div>
        `;
        container.appendChild(projectDiv);
      }

      // Remove project
      function removeProject(button) {
        button.closest(".project-item").remove();
        updateProgress();
      }

      // Update progress bar
      function updateProgress() {
        const fields = ["fullName", "location", "bio", "skills"];

        let filledFields = 0;
        let totalFields = fields.length;

        // Check basic fields
        fields.forEach((fieldId) => {
          const field = document.getElementById(fieldId);
          if (field && field.value.trim()) {
            filledFields++;
          }
        });

        // Check if at least one project is added
        const projects = document.querySelectorAll(".project-item");
        if (projects.length > 0) {
          filledFields++;
          totalFields++;
        }

        // Check if at least one social link is added
        const socialFields = ["website", "linkedin", "github", "behance"];
        const hasSocialLink = socialFields.some((fieldId) => {
          const field = document.getElementById(fieldId);
          return field && field.value.trim();
        });

        if (hasSocialLink) {
          filledFields++;
        }
        totalFields++;

        const percentage = Math.round((filledFields / totalFields) * 100);
        document.getElementById("progressFill").style.width = percentage + "%";
        document.getElementById("progressText").textContent =
          percentage + "% Complete";
      }

      // Save profile
      function saveProfile() {
        const profileData = collectFormData();

        if (!validateRequiredFields()) {
          alert("Please fill in all required fields (marked with *)");
          return;
        }

        $.ajax({
          url: "/api/freelancer/profile",
          type: "PUT",
          contentType: "application/json",
          data: JSON.stringify(profileData),
          success: function (response) {
            alert("Profile saved successfully! 🎉");
            updateProgress();
          },
          error: function () {
            alert("Failed to save profile. Please try again.");
          },
        });
      }

      // Collect form data
      function collectFormData() {
        // Collect projects
        const projects = [];
        document.querySelectorAll(".project-item").forEach((item) => {
          const title = item.querySelector(".project-title").value.trim();
          if (title) {
            projects.push({
              title: title,
              client: item.querySelector(".project-client").value.trim(),
              description: item
                .querySelector(".project-description")
                .value.trim(),
              completedDate: item.querySelector(".project-date").value,
              rating:
                parseInt(item.querySelector(".project-rating").value) || null,
              testimonial: item
                .querySelector(".project-testimonial")
                .value.trim(),
            });
          }
        });

        return {
          name: document.getElementById("fullName").value.trim(),
          profileData: {
            location: document.getElementById("location").value.trim(),
            bio: document.getElementById("bio").value.trim(),
            skills: document.getElementById("skills").value.trim(),
            experience: document.getElementById("experience").value,
            hourlyRate:
              parseFloat(document.getElementById("hourlyRate").value) || null,
            availability: document.getElementById("availability").value,
            completedProjects: projects,
            socialLinks: {
              website: document.getElementById("website").value.trim(),
              linkedin: document.getElementById("linkedin").value.trim(),
              github: document.getElementById("github").value.trim(),
              behance: document.getElementById("behance").value.trim(),
            },
          },
        };
      }

      // Validate required fields
      function validateRequiredFields() {
        const requiredFields = ["fullName", "location", "bio", "skills"];
        return requiredFields.every((fieldId) => {
          const field = document.getElementById(fieldId);
          return field && field.value.trim();
        });
      }

      // Preview profile
      function previewProfile() {
        if (!validateRequiredFields()) {
          alert("Please fill in all required fields before previewing");
          return;
        }

        saveProfile();
        setTimeout(() => {
          window.open(
            `/freelancer-profile.html?id=${currentUser.id}`,
            "_blank"
          );
        }, 1000);
      }

      // Skip for now
      function skipForNow() {
        if (
          confirm(
            "Are you sure you want to skip profile setup? You can complete it later from your dashboard."
          )
        ) {
          window.location.href = "/";
        }
      }

      // Logout
      function logout() {
        $.ajax({
          url: "/api/logout",
          type: "POST",
          success: function () {
            window.location.href = "/login.html";
          },
        });
      }

      // Initialize
      $(document).ready(function () {
        checkAuth();

        // Add event listeners for progress tracking
        const trackFields = [
          "fullName",
          "location",
          "bio",
          "skills",
          "website",
          "linkedin",
          "github",
          "behance",
        ];
        trackFields.forEach((fieldId) => {
          const field = document.getElementById(fieldId);
          if (field) {
            field.addEventListener("input", updateProgress);
            field.addEventListener("change", updateProgress);
          }
        });
      });
    </script>
  </body>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: "Arial", sans-serif;
      background: #f8f9fa;
      color: #333;
      line-height: 1.6;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 0 20px;
    }

    /* Navigation */
    .navbar {
      background: white;
      padding: 1rem 0;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .nav-brand h2 {
      color: #667eea;
      font-size: 1.5rem;
    }

    .nav-links {
      display: flex;
      align-items: center;
      gap: 2rem;
    }

    .nav-links a {
      text-decoration: none;
      color: #333;
      font-weight: 500;
      transition: color 0.3s;
      padding: 0.5rem 1rem;
      border-radius: 5px;
    }

    .nav-links a:hover {
      color: #667eea;
      background: #f0f4ff;
    }

    .logout-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 5px;
      cursor: pointer;
    }

    /* Welcome Header */
    .welcome-header {
      text-align: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 3rem 2rem;
      border-radius: 15px;
      margin-bottom: 2rem;
    }

    .welcome-header h1 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }

    .welcome-header p {
      font-size: 1.2rem;
      opacity: 0.9;
    }

    /* Progress Section */
    .progress-section {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      text-align: center;
    }

    .progress-section h3 {
      margin-bottom: 1rem;
      color: #333;
    }

    .progress-bar {
      width: 100%;
      height: 20px;
      background: #e9ecef;
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 1rem;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
      width: 0%;
      transition: width 0.5s ease;
      border-radius: 10px;
    }

    .progress-text {
      font-weight: bold;
      color: #28a745;
      font-size: 1.1rem;
    }

    /* Profile Form */
    .profile-form {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
    }

    .form-section {
      margin-bottom: 3rem;
      padding-bottom: 2rem;
      border-bottom: 1px solid #eee;
    }

    .form-section:last-of-type {
      border-bottom: none;
      margin-bottom: 0;
    }

    .form-section h3 {
      color: #333;
      margin-bottom: 1.5rem;
      font-size: 1.3rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      margin-bottom: 0.5rem;
      font-weight: 600;
      color: #333;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 12px;
      border: 2px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      transition: border-color 0.3s;
      font-family: inherit;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: #667eea;
    }

    .form-group small {
      margin-top: 0.3rem;
      color: #666;
      font-size: 0.9rem;
    }

    /* Project Items */
    .project-item {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 10px;
      margin-bottom: 1.5rem;
      border: 1px solid #e9ecef;
    }

    .project-item h4 {
      color: #333;
      margin-bottom: 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .remove-project {
      background: #dc3545;
      color: white;
      border: none;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      cursor: pointer;
      font-size: 1.2rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .add-project-btn {
      background: #28a745;
      color: white;
      border: none;
      padding: 1rem 2rem;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 600;
      transition: all 0.3s;
    }

    .add-project-btn:hover {
      background: #218838;
      transform: translateY(-2px);
    }

    /* Form Actions */
    .form-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
      margin-top: 2rem;
      padding-top: 2rem;
      border-top: 1px solid #eee;
    }

    .save-btn,
    .preview-btn,
    .skip-btn {
      padding: 1rem 2rem;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 600;
      transition: all 0.3s;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    .save-btn {
      background: #667eea;
      color: white;
    }

    .save-btn:hover {
      background: #5a6fd8;
      transform: translateY(-2px);
    }

    .preview-btn {
      background: #17a2b8;
      color: white;
    }

    .preview-btn:hover {
      background: #138496;
      transform: translateY(-2px);
    }

    .skip-btn {
      background: #6c757d;
      color: white;
    }

    .skip-btn:hover {
      background: #5a6268;
      transform: translateY(-2px);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .container {
        padding: 0 15px;
      }

      .welcome-header h1 {
        font-size: 2rem;
      }

      .welcome-header p {
        font-size: 1rem;
      }

      .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .nav-links {
        flex-direction: column;
        gap: 1rem;
      }

      .form-actions {
        flex-direction: column;
        align-items: center;
      }

      .save-btn,
      .preview-btn,
      .skip-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
      }
    }

    /* Animation for form sections */
    .form-section {
      animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Required field indicator */
    .form-group label:after {
      content: " *";
      color: #dc3545;
      font-weight: bold;
    }

    .form-group
      label:not([for="experience"]):not([for="hourlyRate"]):not(
        [for="availability"]
      ):not([for="website"]):not([for="linkedin"]):not([for="github"]):not(
        [for="behance"]
      ):after,
    .form-group label[for="fullName"]:after,
    .form-group label[for="location"]:after,
    .form-group label[for="bio"]:after,
    .form-group label[for="skills"]:after {
      content: " *";
      color: #dc3545;
    }

    /* Remove asterisk from optional fields */
    .project-item label:after {
      content: "";
    }
  </style>
</html>
