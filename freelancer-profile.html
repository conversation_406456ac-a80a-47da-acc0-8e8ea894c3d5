<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Freelancer Profile - Freelancer Platform</title>
    <script
      src="https://code.jquery.com/jquery-3.4.1.js"
      integrity="sha256-WpOohJOqMqqyKL9FccASB9O0KwACQJpFTUBLTYOVvVU="
      crossorigin="anonymous"
    ></script>
  </head>
  <body>
    <div class="container">
      <!-- Navigation -->
      <nav class="navbar">
        <div class="nav-brand">
          <h2>FreelancerHub</h2>
        </div>
        <div class="nav-links">
          <a href="/">Home</a>
          <a href="/freelancers-browse.html">Browse Freelancers</a>
          <a href="/chat.html">Messages</a>
          <span id="userInfo">Loading...</span>
          <button onclick="logout()" class="logout-btn">Logout</button>
        </div>
      </nav>

      <!-- Profile Header -->
      <div class="profile-header">
        <div class="profile-avatar">
          <img
            id="profileImage"
            src="https://via.placeholder.com/150"
            alt="Profile Picture"
          />
          <div class="online-status"></div>
        </div>
        <div class="profile-info">
          <h1 id="freelancerName">Loading...</h1>
          <p class="title" id="freelancerTitle">Loading...</p>
          <p class="location" id="freelancerLocation">📍 Loading...</p>
          <div class="rating">
            <span class="stars" id="freelancerRating">⭐⭐⭐⭐⭐</span>
            <span class="rating-text">(4.9/5)</span>
          </div>
          <p class="hourly-rate" id="hourlyRate">$50/hour</p>
        </div>
        <div class="profile-actions">
          <button id="followBtn" class="btn-follow" onclick="toggleFollow()">
            <span id="followText">Follow</span>
          </button>
          <button class="btn-chat" onclick="startChat()">💬 Chat</button>
          <button class="btn-hire">🚀 Hire Now</button>
        </div>
      </div>

      <!-- Profile Stats -->
      <div class="profile-stats">
        <div class="stat-item">
          <div class="stat-number" id="followersCount">0</div>
          <div class="stat-label">Followers</div>
        </div>
        <div class="stat-item">
          <div class="stat-number" id="followingCount">0</div>
          <div class="stat-label">Following</div>
        </div>
        <div class="stat-item">
          <div class="stat-number" id="projectsCount">0</div>
          <div class="stat-label">Projects</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">100%</div>
          <div class="stat-label">Success Rate</div>
        </div>
      </div>

      <!-- Profile Content -->
      <div class="profile-content">
        <!-- About Section -->
        <div class="content-section">
          <h3>About Me</h3>
          <p id="freelancerBio">Loading bio...</p>

          <div class="skills-section">
            <h4>Skills & Expertise</h4>
            <div class="skills-container" id="skillsContainer">
              <!-- Skills will be loaded here -->
            </div>
          </div>

          <div class="experience-section">
            <h4>Experience Level</h4>
            <p id="experienceLevel">Loading...</p>
          </div>
        </div>

        <!-- Portfolio Section -->
        <div class="content-section">
          <h3>Portfolio & Completed Projects</h3>
          <div class="portfolio-grid" id="portfolioGrid">
            <!-- Portfolio items will be loaded here -->
          </div>
        </div>

        <!-- Social Links -->
        <div class="content-section">
          <h3>Connect With Me</h3>
          <div class="social-links" id="socialLinks">
            <!-- Social links will be loaded here -->
          </div>
        </div>

        <!-- Followers/Following -->
        <div class="content-section">
          <div class="social-tabs">
            <button class="tab-btn active" onclick="showSocialTab('followers')">
              Followers
            </button>
            <button class="tab-btn" onclick="showSocialTab('following')">
              Following
            </button>
          </div>

          <div id="followers-tab" class="social-tab-content active">
            <div class="users-grid" id="followersGrid">
              <!-- Followers will be loaded here -->
            </div>
          </div>

          <div id="following-tab" class="social-tab-content">
            <div class="users-grid" id="followingGrid">
              <!-- Following will be loaded here -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      let currentUser = null;
      let freelancerId = null;
      let freelancerData = null;

      // Get freelancer ID from URL parameters
      function getFreelancerId() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get("id");
      }

      // Check authentication
      function checkAuth() {
        $.ajax({
          url: "/api/auth/status",
          type: "GET",
          success: function (response) {
            if (response.authenticated) {
              currentUser = response.user;
              document.getElementById(
                "userInfo"
              ).textContent = `Welcome, ${currentUser.name}`;
              loadFreelancerProfile();
            } else {
              window.location.href = "/login.html";
            }
          },
          error: function () {
            window.location.href = "/login.html";
          },
        });
      }

      // Load freelancer profile
      function loadFreelancerProfile() {
        freelancerId = getFreelancerId();
        if (!freelancerId) {
          alert("Freelancer ID not provided");
          window.location.href = "/";
          return;
        }

        $.ajax({
          url: `/api/freelancer/${freelancerId}`,
          type: "GET",
          success: function (data) {
            freelancerData = data;
            displayFreelancerProfile(data);
          },
          error: function () {
            alert("Failed to load freelancer profile");
            window.location.href = "/";
          },
        });
      }

      // Display freelancer profile
      function displayFreelancerProfile(freelancer) {
        document.getElementById("freelancerName").textContent = freelancer.name;
        document.getElementById("freelancerTitle").textContent =
          freelancer.profileData?.skills || "Freelancer";
        document.getElementById("freelancerLocation").textContent = `📍 ${
          freelancer.profileData?.location || "Location not specified"
        }`;
        document.getElementById("freelancerBio").textContent =
          freelancer.profileData?.bio || "No bio available";
        document.getElementById("hourlyRate").textContent = `$${
          freelancer.profileData?.hourlyRate || "N/A"
        }/hour`;
        document.getElementById("experienceLevel").textContent =
          freelancer.profileData?.experience || "Not specified";

        // Update stats
        document.getElementById("followersCount").textContent =
          freelancer.followers?.length || 0;
        document.getElementById("followingCount").textContent =
          freelancer.following?.length || 0;
        document.getElementById("projectsCount").textContent =
          freelancer.profileData?.completedProjects?.length || 0;

        // Update follow button
        const isFollowing =
          currentUser &&
          freelancer.followers?.some((f) => f._id === currentUser.id);
        updateFollowButton(isFollowing);

        // Display skills
        displaySkills(freelancer.profileData?.skills);

        // Display portfolio
        displayPortfolio(freelancer.profileData?.completedProjects);

        // Display social links
        displaySocialLinks(freelancer.profileData?.socialLinks);

        // Display followers/following
        displayFollowers(freelancer.followers);
        displayFollowing(freelancer.following);
      }

      // Display skills
      function displaySkills(skillsString) {
        const container = document.getElementById("skillsContainer");
        if (skillsString) {
          const skills = skillsString.split(",").map((s) => s.trim());
          container.innerHTML = skills
            .map((skill) => `<span class="skill-tag">${skill}</span>`)
            .join("");
        } else {
          container.innerHTML = "<p>No skills listed</p>";
        }
      }

      // Display portfolio
      function displayPortfolio(projects) {
        const container = document.getElementById("portfolioGrid");
        if (projects && projects.length > 0) {
          container.innerHTML = projects
            .map(
              (project) => `
            <div class="portfolio-item">
              <div class="project-image">
                <img src="${
                  project.projectImage || "https://via.placeholder.com/300x200"
                }" alt="${project.title}" />
              </div>
              <div class="project-info">
                <h4>${project.title}</h4>
                <p>${project.description}</p>
                <div class="project-meta">
                  <span class="client">Client: ${project.client}</span>
                  <span class="rating">⭐ ${project.rating || "N/A"}</span>
                </div>
                ${
                  project.testimonial
                    ? `<blockquote>"${project.testimonial}"</blockquote>`
                    : ""
                }
              </div>
            </div>
          `
            )
            .join("");
        } else {
          container.innerHTML = "<p>No portfolio items to display</p>";
        }
      }

      // Display social links
      function displaySocialLinks(socialLinks) {
        const container = document.getElementById("socialLinks");
        if (socialLinks) {
          let linksHtml = "";
          if (socialLinks.website)
            linksHtml += `<a href="${socialLinks.website}" target="_blank" class="social-link">🌐 Website</a>`;
          if (socialLinks.linkedin)
            linksHtml += `<a href="${socialLinks.linkedin}" target="_blank" class="social-link">💼 LinkedIn</a>`;
          if (socialLinks.github)
            linksHtml += `<a href="${socialLinks.github}" target="_blank" class="social-link">💻 GitHub</a>`;
          if (socialLinks.behance)
            linksHtml += `<a href="${socialLinks.behance}" target="_blank" class="social-link">🎨 Behance</a>`;

          container.innerHTML = linksHtml || "<p>No social links provided</p>";
        } else {
          container.innerHTML = "<p>No social links provided</p>";
        }
      }

      // Display followers
      function displayFollowers(followers) {
        const container = document.getElementById("followersGrid");
        if (followers && followers.length > 0) {
          container.innerHTML = followers
            .map(
              (follower) => `
            <div class="user-card">
              <img src="${
                follower.profileData?.profileImage ||
                "https://via.placeholder.com/50"
              }" alt="${follower.name}" />
              <div class="user-info">
                <h4>${follower.name}</h4>
                <a href="/freelancer-profile.html?id=${
                  follower._id
                }">View Profile</a>
              </div>
            </div>
          `
            )
            .join("");
        } else {
          container.innerHTML = "<p>No followers yet</p>";
        }
      }

      // Display following
      function displayFollowing(following) {
        const container = document.getElementById("followingGrid");
        if (following && following.length > 0) {
          container.innerHTML = following
            .map(
              (user) => `
            <div class="user-card">
              <img src="${
                user.profileData?.profileImage ||
                "https://via.placeholder.com/50"
              }" alt="${user.name}" />
              <div class="user-info">
                <h4>${user.name}</h4>
                <a href="/freelancer-profile.html?id=${
                  user._id
                }">View Profile</a>
              </div>
            </div>
          `
            )
            .join("");
        } else {
          container.innerHTML = "<p>Not following anyone yet</p>";
        }
      }

      // Toggle follow
      function toggleFollow() {
        if (!currentUser) {
          alert("Please login to follow freelancers");
          return;
        }

        $.ajax({
          url: `/api/freelancer/${freelancerId}/follow`,
          type: "POST",
          success: function (response) {
            updateFollowButton(response.isFollowing);
            // Update follower count
            const currentCount = parseInt(
              document.getElementById("followersCount").textContent
            );
            document.getElementById("followersCount").textContent =
              response.isFollowing ? currentCount + 1 : currentCount - 1;
          },
          error: function () {
            alert("Failed to update follow status");
          },
        });
      }

      // Update follow button
      function updateFollowButton(isFollowing) {
        const btn = document.getElementById("followBtn");
        const text = document.getElementById("followText");

        if (isFollowing) {
          btn.classList.add("following");
          text.textContent = "Following";
        } else {
          btn.classList.remove("following");
          text.textContent = "Follow";
        }
      }

      // Start chat
      function startChat() {
        if (!currentUser) {
          alert("Please login to start a chat");
          return;
        }

        if (currentUser.id === freelancerId) {
          alert("You cannot chat with yourself");
          return;
        }

        $.ajax({
          url: "/api/chat",
          type: "POST",
          contentType: "application/json",
          data: JSON.stringify({ recipientId: freelancerId }),
          success: function (response) {
            window.location.href = `/chat.html?chatId=${response._id}`;
          },
          error: function () {
            alert("Failed to start chat");
          },
        });
      }

      // Show social tab
      function showSocialTab(tabName) {
        document.querySelectorAll(".social-tab-content").forEach((tab) => {
          tab.classList.remove("active");
        });
        document.querySelectorAll(".tab-btn").forEach((btn) => {
          btn.classList.remove("active");
        });

        document.getElementById(tabName + "-tab").classList.add("active");
        event.target.classList.add("active");
      }

      // Logout
      function logout() {
        $.ajax({
          url: "/api/logout",
          type: "POST",
          success: function () {
            window.location.href = "/login.html";
          },
        });
      }

      // Initialize
      $(document).ready(function () {
        checkAuth();
      });
    </script>
  </body>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: "Arial", sans-serif;
      background: #f8f9fa;
      color: #333;
      line-height: 1.6;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    /* Navigation */
    .navbar {
      background: white;
      padding: 1rem 0;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .nav-brand h2 {
      color: #667eea;
      font-size: 1.5rem;
    }

    .nav-links {
      display: flex;
      align-items: center;
      gap: 2rem;
    }

    .nav-links a {
      text-decoration: none;
      color: #333;
      font-weight: 500;
      transition: color 0.3s;
    }

    .nav-links a:hover {
      color: #667eea;
    }

    .logout-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 5px;
      cursor: pointer;
    }

    /* Profile Header */
    .profile-header {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      display: grid;
      grid-template-columns: auto 1fr auto;
      gap: 2rem;
      align-items: center;
    }

    .profile-avatar {
      position: relative;
    }

    .profile-avatar img {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      object-fit: cover;
      border: 4px solid #667eea;
    }

    .online-status {
      position: absolute;
      bottom: 10px;
      right: 10px;
      width: 20px;
      height: 20px;
      background: #28a745;
      border-radius: 50%;
      border: 3px solid white;
    }

    .profile-info h1 {
      font-size: 2rem;
      margin-bottom: 0.5rem;
      color: #333;
    }

    .title {
      font-size: 1.2rem;
      color: #667eea;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .location {
      color: #666;
      margin-bottom: 1rem;
    }

    .rating {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 1rem;
    }

    .stars {
      color: #ffc107;
    }

    .hourly-rate {
      font-size: 1.5rem;
      font-weight: bold;
      color: #28a745;
    }

    .profile-actions {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .btn-follow,
    .btn-chat,
    .btn-hire {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s;
      text-decoration: none;
      text-align: center;
    }

    .btn-follow {
      background: #667eea;
      color: white;
    }

    .btn-follow.following {
      background: #28a745;
    }

    .btn-chat {
      background: #17a2b8;
      color: white;
    }

    .btn-hire {
      background: #28a745;
      color: white;
    }

    .btn-follow:hover,
    .btn-chat:hover,
    .btn-hire:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    /* Profile Stats */
    .profile-stats {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .stat-item {
      background: white;
      padding: 1.5rem;
      border-radius: 10px;
      text-align: center;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    }

    .stat-number {
      font-size: 2rem;
      font-weight: bold;
      color: #667eea;
      margin-bottom: 0.5rem;
    }

    .stat-label {
      color: #666;
      font-size: 0.9rem;
    }

    /* Content Sections */
    .content-section {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
    }

    .content-section h3 {
      color: #333;
      margin-bottom: 1.5rem;
      font-size: 1.5rem;
      border-bottom: 2px solid #667eea;
      padding-bottom: 0.5rem;
    }

    .content-section h4 {
      color: #333;
      margin: 1.5rem 0 1rem 0;
    }

    /* Skills */
    .skills-container {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .skill-tag {
      background: #e3f2fd;
      color: #1976d2;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.9rem;
      font-weight: 500;
    }

    /* Portfolio */
    .portfolio-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .portfolio-item {
      border: 1px solid #eee;
      border-radius: 10px;
      overflow: hidden;
      transition: transform 0.3s;
    }

    .portfolio-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .project-image img {
      width: 100%;
      height: 200px;
      object-fit: cover;
    }

    .project-info {
      padding: 1.5rem;
    }

    .project-info h4 {
      margin: 0 0 0.5rem 0;
      color: #333;
    }

    .project-meta {
      display: flex;
      justify-content: space-between;
      margin: 1rem 0;
      font-size: 0.9rem;
      color: #666;
    }

    blockquote {
      background: #f8f9fa;
      padding: 1rem;
      border-left: 4px solid #667eea;
      margin-top: 1rem;
      font-style: italic;
    }

    /* Social Links */
    .social-links {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .social-link {
      display: inline-block;
      padding: 0.75rem 1.5rem;
      background: #f8f9fa;
      color: #333;
      text-decoration: none;
      border-radius: 8px;
      transition: all 0.3s;
    }

    .social-link:hover {
      background: #667eea;
      color: white;
      transform: translateY(-2px);
    }

    /* Social Tabs */
    .social-tabs {
      display: flex;
      margin-bottom: 2rem;
      border-bottom: 1px solid #eee;
    }

    .tab-btn {
      padding: 1rem 2rem;
      border: none;
      background: none;
      cursor: pointer;
      font-size: 1rem;
      color: #666;
      border-bottom: 2px solid transparent;
      transition: all 0.3s;
    }

    .tab-btn.active {
      color: #667eea;
      border-bottom-color: #667eea;
    }

    .social-tab-content {
      display: none;
    }

    .social-tab-content.active {
      display: block;
    }

    .users-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 1rem;
    }

    .user-card {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 10px;
      transition: all 0.3s;
    }

    .user-card:hover {
      background: #e9ecef;
      transform: translateY(-2px);
    }

    .user-card img {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
    }

    .user-info h4 {
      margin: 0 0 0.5rem 0;
      font-size: 1rem;
    }

    .user-info a {
      color: #667eea;
      text-decoration: none;
      font-size: 0.9rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .profile-header {
        grid-template-columns: 1fr;
        text-align: center;
      }

      .profile-stats {
        grid-template-columns: repeat(2, 1fr);
      }

      .nav-links {
        flex-direction: column;
        gap: 1rem;
      }

      .portfolio-grid {
        grid-template-columns: 1fr;
      }

      .users-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</html>
