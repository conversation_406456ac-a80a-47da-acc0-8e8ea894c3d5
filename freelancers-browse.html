<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Browse Freelancers - Freelancer Platform</title>
    <script
      src="https://code.jquery.com/jquery-3.4.1.js"
      integrity="sha256-WpOohJOqMqqyKL9FccASB9O0KwACQJpFTUBLTYOVvVU="
      crossorigin="anonymous"
    ></script>
  </head>
  <body>
    <div class="container">
      <!-- Navigation -->
      <nav class="navbar">
        <div class="nav-brand">
          <h2>FreelancerHub</h2>
        </div>
        <div class="nav-links">
          <a href="/">Home</a>
          <a href="/freelancers-browse.html" class="active"
            >Browse Freelancers</a
          >
          <a href="/chat.html">Messages</a>
          <span id="userInfo">Loading...</span>
          <button onclick="logout()" class="logout-btn">Logout</button>
        </div>
      </nav>

      <!-- Header -->
      <div class="page-header">
        <h1>Discover Talented Freelancers</h1>
        <p>Connect with skilled professionals from around the world</p>
      </div>

      <!-- Search and Filters -->
      <div class="search-section">
        <div class="search-bar">
          <input
            type="text"
            id="searchInput"
            placeholder="Search by skills, name, or location..."
          />
          <button onclick="searchFreelancers()" class="search-btn">
            🔍 Search
          </button>
        </div>

        <div class="filters">
          <select id="experienceFilter">
            <option value="">All Experience Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="expert">Expert</option>
          </select>

          <select id="rateFilter">
            <option value="">All Rates</option>
            <option value="0-25">$0 - $25/hr</option>
            <option value="25-50">$25 - $50/hr</option>
            <option value="50-100">$50 - $100/hr</option>
            <option value="100+">$100+/hr</option>
          </select>

          <button onclick="clearFilters()" class="clear-btn">
            Clear Filters
          </button>
        </div>
      </div>

      <!-- Freelancers Grid -->
      <div class="freelancers-grid" id="freelancersGrid">
        <!-- Freelancers will be loaded here -->
      </div>

      <!-- Loading indicator -->
      <div id="loading" class="loading">
        <div class="spinner"></div>
        <p>Loading freelancers...</p>
      </div>

      <!-- No results message -->
      <div id="noResults" class="no-results" style="display: none">
        <h3>No freelancers found</h3>
        <p>Try adjusting your search criteria or filters</p>
      </div>
    </div>

    <script>
      let currentUser = null;
      let allFreelancers = [];
      let filteredFreelancers = [];

      // Check authentication
      function checkAuth() {
        $.ajax({
          url: "/api/auth/status",
          type: "GET",
          success: function (response) {
            if (response.authenticated) {
              currentUser = response.user;
              document.getElementById(
                "userInfo"
              ).textContent = `Welcome, ${currentUser.name}`;
              loadFreelancers();
            } else {
              window.location.href = "/login.html";
            }
          },
          error: function () {
            window.location.href = "/login.html";
          },
        });
      }

      // Load all freelancers
      function loadFreelancers() {
        document.getElementById("loading").style.display = "block";

        $.ajax({
          url: "/api/freelancers/browse",
          type: "GET",
          success: function (data) {
            allFreelancers = data;
            filteredFreelancers = data;
            displayFreelancers(data);
            document.getElementById("loading").style.display = "none";
          },
          error: function () {
            alert("Failed to load freelancers");
            document.getElementById("loading").style.display = "none";
          },
        });
      }

      // Display freelancers
      function displayFreelancers(freelancers) {
        const grid = document.getElementById("freelancersGrid");
        const noResults = document.getElementById("noResults");

        if (freelancers.length === 0) {
          grid.innerHTML = "";
          noResults.style.display = "block";
          return;
        }

        noResults.style.display = "none";

        grid.innerHTML = freelancers
          .map(
            (freelancer) => `
          <div class="freelancer-card">
            <div class="card-header">
              <img src="${
                freelancer.profileData?.profileImage ||
                "https://via.placeholder.com/80"
              }" 
                   alt="${freelancer.name}" class="avatar" />
              <div class="online-indicator"></div>
            </div>
            
            <div class="card-content">
              <h3>${freelancer.name}</h3>
              <p class="title">${
                freelancer.profileData?.skills?.split(",")[0] || "Freelancer"
              }</p>
              <p class="location">📍 ${
                freelancer.profileData?.location || "Location not specified"
              }</p>
              
              <div class="skills-preview">
                ${
                  freelancer.profileData?.skills
                    ? freelancer.profileData.skills
                        .split(",")
                        .slice(0, 3)
                        .map(
                          (skill) =>
                            `<span class="skill-tag">${skill.trim()}</span>`
                        )
                        .join("")
                    : '<span class="skill-tag">No skills listed</span>'
                }
              </div>
              
              <div class="card-stats">
                <div class="stat">
                  <span class="stat-value">${
                    freelancer.followers?.length || 0
                  }</span>
                  <span class="stat-label">Followers</span>
                </div>
                <div class="stat">
                  <span class="stat-value">${
                    freelancer.profileData?.completedProjects?.length || 0
                  }</span>
                  <span class="stat-label">Projects</span>
                </div>
                <div class="stat">
                  <span class="stat-value">⭐ 4.9</span>
                  <span class="stat-label">Rating</span>
                </div>
              </div>
              
              <div class="card-footer">
                <div class="rate">
                  <strong>$${
                    freelancer.profileData?.hourlyRate || "N/A"
                  }/hr</strong>
                </div>
                <div class="card-actions">
                  <button onclick="viewProfile('${
                    freelancer._id
                  }')" class="btn-view">
                    View Profile
                  </button>
                  <button onclick="startChat('${
                    freelancer._id
                  }')" class="btn-chat">
                    💬
                  </button>
                </div>
              </div>
            </div>
          </div>
        `
          )
          .join("");
      }

      // Search freelancers
      function searchFreelancers() {
        const searchTerm = document
          .getElementById("searchInput")
          .value.toLowerCase();
        const experienceFilter =
          document.getElementById("experienceFilter").value;
        const rateFilter = document.getElementById("rateFilter").value;

        filteredFreelancers = allFreelancers.filter((freelancer) => {
          // Text search
          const matchesSearch =
            !searchTerm ||
            freelancer.name.toLowerCase().includes(searchTerm) ||
            (freelancer.profileData?.skills &&
              freelancer.profileData.skills
                .toLowerCase()
                .includes(searchTerm)) ||
            (freelancer.profileData?.location &&
              freelancer.profileData.location
                .toLowerCase()
                .includes(searchTerm));

          // Experience filter
          const matchesExperience =
            !experienceFilter ||
            freelancer.profileData?.experience === experienceFilter;

          // Rate filter
          let matchesRate = true;
          if (rateFilter && freelancer.profileData?.hourlyRate) {
            const rate = freelancer.profileData.hourlyRate;
            switch (rateFilter) {
              case "0-25":
                matchesRate = rate >= 0 && rate <= 25;
                break;
              case "25-50":
                matchesRate = rate > 25 && rate <= 50;
                break;
              case "50-100":
                matchesRate = rate > 50 && rate <= 100;
                break;
              case "100+":
                matchesRate = rate > 100;
                break;
            }
          }

          return matchesSearch && matchesExperience && matchesRate;
        });

        displayFreelancers(filteredFreelancers);
      }

      // Clear filters
      function clearFilters() {
        document.getElementById("searchInput").value = "";
        document.getElementById("experienceFilter").value = "";
        document.getElementById("rateFilter").value = "";
        filteredFreelancers = allFreelancers;
        displayFreelancers(allFreelancers);
      }

      // View freelancer profile
      function viewProfile(freelancerId) {
        window.location.href = `/freelancer-profile.html?id=${freelancerId}`;
      }

      // Start chat
      function startChat(freelancerId) {
        if (!currentUser) {
          alert("Please login to start a chat");
          return;
        }

        if (currentUser.id === freelancerId) {
          alert("You cannot chat with yourself");
          return;
        }

        $.ajax({
          url: "/api/chat",
          type: "POST",
          contentType: "application/json",
          data: JSON.stringify({ recipientId: freelancerId }),
          success: function (response) {
            window.location.href = `/chat.html?chatId=${response._id}`;
          },
          error: function () {
            alert("Failed to start chat");
          },
        });
      }

      // Logout
      function logout() {
        $.ajax({
          url: "/api/logout",
          type: "POST",
          success: function () {
            window.location.href = "/login.html";
          },
        });
      }

      // Add event listeners
      $(document).ready(function () {
        checkAuth();

        // Search on Enter key
        document
          .getElementById("searchInput")
          .addEventListener("keypress", function (e) {
            if (e.key === "Enter") {
              searchFreelancers();
            }
          });

        // Filter on change
        document
          .getElementById("experienceFilter")
          .addEventListener("change", searchFreelancers);
        document
          .getElementById("rateFilter")
          .addEventListener("change", searchFreelancers);
      });
    </script>
  </body>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: "Arial", sans-serif;
      background: #f8f9fa;
      color: #333;
      line-height: 1.6;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    /* Navigation */
    .navbar {
      background: white;
      padding: 1rem 0;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .nav-brand h2 {
      color: #667eea;
      font-size: 1.5rem;
    }

    .nav-links {
      display: flex;
      align-items: center;
      gap: 2rem;
    }

    .nav-links a {
      text-decoration: none;
      color: #333;
      font-weight: 500;
      transition: color 0.3s;
      padding: 0.5rem 1rem;
      border-radius: 5px;
    }

    .nav-links a:hover,
    .nav-links a.active {
      color: #667eea;
      background: #f0f4ff;
    }

    .logout-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 5px;
      cursor: pointer;
    }

    /* Page Header */
    .page-header {
      text-align: center;
      margin-bottom: 3rem;
    }

    .page-header h1 {
      font-size: 2.5rem;
      color: #333;
      margin-bottom: 1rem;
    }

    .page-header p {
      font-size: 1.2rem;
      color: #666;
    }

    /* Search Section */
    .search-section {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      margin-bottom: 3rem;
    }

    .search-bar {
      display: flex;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .search-bar input {
      flex: 1;
      padding: 1rem;
      border: 2px solid #ddd;
      border-radius: 10px;
      font-size: 1rem;
      transition: border-color 0.3s;
    }

    .search-bar input:focus {
      outline: none;
      border-color: #667eea;
    }

    .search-btn {
      padding: 1rem 2rem;
      background: #667eea;
      color: white;
      border: none;
      border-radius: 10px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s;
    }

    .search-btn:hover {
      background: #5a6fd8;
      transform: translateY(-2px);
    }

    .filters {
      display: flex;
      gap: 1rem;
      align-items: center;
      flex-wrap: wrap;
    }

    .filters select {
      padding: 0.75rem;
      border: 2px solid #ddd;
      border-radius: 8px;
      font-size: 0.9rem;
      background: white;
      cursor: pointer;
    }

    .clear-btn {
      padding: 0.75rem 1.5rem;
      background: #6c757d;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 0.9rem;
    }

    /* Freelancers Grid */
    .freelancers-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;
    }

    .freelancer-card {
      background: white;
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      position: relative;
    }

    .freelancer-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    .card-header {
      position: relative;
      text-align: center;
      padding: 2rem 1rem 1rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      object-fit: cover;
      border: 4px solid white;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .online-indicator {
      position: absolute;
      top: 2.5rem;
      right: calc(50% - 35px);
      width: 16px;
      height: 16px;
      background: #28a745;
      border-radius: 50%;
      border: 3px solid white;
    }

    .card-content {
      padding: 1.5rem;
    }

    .card-content h3 {
      font-size: 1.3rem;
      margin-bottom: 0.5rem;
      color: #333;
      text-align: center;
    }

    .title {
      color: #667eea;
      font-weight: 600;
      text-align: center;
      margin-bottom: 0.5rem;
    }

    .location {
      color: #666;
      text-align: center;
      margin-bottom: 1rem;
      font-size: 0.9rem;
    }

    .skills-preview {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      justify-content: center;
      margin-bottom: 1.5rem;
    }

    .skill-tag {
      background: #e3f2fd;
      color: #1976d2;
      padding: 0.3rem 0.8rem;
      border-radius: 15px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .card-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1rem;
      margin-bottom: 1.5rem;
      text-align: center;
    }

    .stat-value {
      display: block;
      font-weight: bold;
      color: #333;
      font-size: 1.1rem;
    }

    .stat-label {
      font-size: 0.8rem;
      color: #666;
    }

    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 1rem;
      border-top: 1px solid #eee;
    }

    .rate {
      color: #28a745;
      font-size: 1.1rem;
    }

    .card-actions {
      display: flex;
      gap: 0.5rem;
    }

    .btn-view {
      padding: 0.5rem 1rem;
      background: #667eea;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 0.9rem;
      transition: all 0.3s;
    }

    .btn-view:hover {
      background: #5a6fd8;
    }

    .btn-chat {
      padding: 0.5rem;
      background: #17a2b8;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      width: 40px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;
    }

    .btn-chat:hover {
      background: #138496;
    }

    /* Loading */
    .loading {
      text-align: center;
      padding: 3rem;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    /* No Results */
    .no-results {
      text-align: center;
      padding: 3rem;
      color: #666;
    }

    .no-results h3 {
      margin-bottom: 1rem;
      color: #333;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .freelancers-grid {
        grid-template-columns: 1fr;
      }

      .nav-links {
        flex-direction: column;
        gap: 1rem;
      }

      .search-bar {
        flex-direction: column;
      }

      .filters {
        justify-content: center;
      }

      .page-header h1 {
        font-size: 2rem;
      }
    }
  </style>
</html>
