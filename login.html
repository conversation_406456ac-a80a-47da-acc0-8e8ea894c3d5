<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login - Freelancer Platform</title>
    <script
      src="https://code.jquery.com/jquery-3.4.1.js"
      integrity="sha256-WpOohJOqMqqyKL9FccASB9O0KwACQJpFTUBLTYOVvVU="
      crossorigin="anonymous"
    ></script>
    <script>
      function LoginForm() {
        const email = document.getElementById("Email").value;
        const password = document.getElementById("Password").value;

        // Basic validation
        if (!email || !password) {
          alert("Please fill in all fields");
          return;
        }

        // Send data to our backend
        $.ajax({
          url: "/api/login",
          type: "POST",
          contentType: "application/json",
          data: JSON.stringify({
            email: email,
            password: password,
          }),
          success: function (response) {
            alert("Login successful! Welcome back " + response.user.name);

            // Check if user is freelancer and redirect accordingly
            if (response.user.role === "freelancer") {
              // Check if profile is complete by trying to get their profile
              $.ajax({
                url: `/api/freelancer/${response.user.id}`,
                type: "GET",
                success: function (profile) {
                  // If profile exists and has basic info, go to home
                  if (
                    profile.profileData &&
                    profile.profileData.bio &&
                    profile.profileData.skills
                  ) {
                    window.location.href = "/";
                  } else {
                    // Profile incomplete, go to dashboard
                    window.location.href = "/freelancer-dashboard.html";
                  }
                },
                error: function () {
                  // Profile doesn't exist, go to dashboard
                  window.location.href = "/freelancer-dashboard.html";
                },
              });
            } else if (response.user.role === "admin") {
              // Admin user, go to admin dashboard
              window.location.href = "/admin.html";
            } else {
              // Regular user, go to user dashboard
              window.location.href = "/user-dashboard.html";
            }
          },
          error: function (xhr) {
            const error = xhr.responseJSON
              ? xhr.responseJSON.error
              : "Login failed";
            alert("Error: " + error);
          },
        });
      }
    </script>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h2>Login</h2>
        <p>"Uniting freelancing"</p>
      </div>
      <form id="loginForm">
        <input
          type="email"
          id="Email"
          name="email"
          placeholder="Email"
          required
        />
        <input
          type="password"
          id="Password"
          name="password"
          placeholder="Password"
          required
        />

        <div class="button-container">
          <button type="button" onclick="LoginForm()">Login</button>
        </div>
      </form>

      <div class="signup-link">
        <p>Don't have an account? <a href="signup.html">Sign up here</a></p>
      </div>
    </div>
  </body>
  <style>
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      font-family: Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .container {
      background: white;
      padding: 2rem;
      border-radius: 10px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 400px;
    }

    .header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .header h2 {
      color: #333;
      margin-bottom: 0.5rem;
    }

    .header p {
      color: #666;
      font-style: italic;
    }

    form {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    input {
      padding: 12px;
      border: 2px solid #ddd;
      border-radius: 5px;
      font-size: 16px;
      transition: border-color 0.3s;
    }

    input:focus {
      outline: none;
      border-color: #667eea;
    }

    .button-container {
      margin-top: 1rem;
    }

    button {
      width: 100%;
      padding: 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 5px;
      font-size: 16px;
      cursor: pointer;
      transition: transform 0.2s;
    }

    button:hover {
      transform: translateY(-2px);
    }

    .signup-link {
      text-align: center;
      margin-top: 1.5rem;
      padding-top: 1rem;
      border-top: 1px solid #eee;
    }

    .signup-link a {
      color: #667eea;
      text-decoration: none;
    }

    .signup-link a:hover {
      text-decoration: underline;
    }
  </style>
</html>
