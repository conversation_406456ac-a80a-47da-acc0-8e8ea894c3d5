{"version": 3, "file": "auto_encrypter.js", "sourceRoot": "", "sources": ["../../src/client-side-encryption/auto_encrypter.ts"], "names": [], "mappings": ";;;;AAMA,kCAAgE;AAEhE,kCAAqD;AACrD,oCAA6C;AAC7C,kDAAuE;AACvE,oCAAsD;AACtD,sDAAsD;AACtD,qCAA0D;AAC1D,+DAA2D;AAC3D,2CAAuE;AACvE,mDAAwE;AA8KxE,cAAc;AACD,QAAA,yBAAyB,GAAG,MAAM,CAAC,MAAM,CAAC;IACrD,UAAU,EAAE,CAAC;IACb,KAAK,EAAE,CAAC;IACR,OAAO,EAAE,CAAC;IACV,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;CACA,CAAC,CAAC;AAiBZ,mEAAmE;AACnE,wEAAwE;AACxE,uEAAuE;AACvE,UAAU;AACV,gBAAgB;AAChB,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;AACrE,gBAAgB;AAChB,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;AAEzD;;;GAGG;AACH,MAAa,aAAa;IA4BxB,gBAAgB;IAChB,MAAM,CAAC,aAAa;QAClB,MAAM,UAAU,GAAG,IAAA,iCAA0B,GAAE,CAAC;QAChD,IAAI,cAAc,IAAI,UAAU,EAAE;YAChC,MAAM,UAAU,CAAC,YAAY,CAAC;SAC/B;QACD,OAAO,UAAU,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+CG;IACH,YAAY,MAAmB,EAAE,OAA8B;QAnE/D;;;;;;;WAOG;QACH,QAAiB,GAAG,KAAK,CAAC;QA4DxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,oBAAoB,KAAK,IAAI,CAAC;QAE/D,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,IAAI,gBAAgB,CAAC;QACxE,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,cAAc,IAAI,MAAM,CAAC;QACxD,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,cAAc,IAAI,MAAM,CAAC;QACxD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC;QAChD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC;QAEhD,MAAM,iBAAiB,GAAsB;YAC3C,eAAe;SAChB,CAAC;QACF,IAAI,OAAO,CAAC,SAAS,EAAE;YACrB,iBAAiB,CAAC,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;gBAC9D,CAAC,CAAC,OAAO,CAAC,SAAS;gBACnB,CAAC,CAAE,IAAA,gBAAS,EAAC,OAAO,CAAC,SAAS,CAAY,CAAC;SAC9C;QAED,IAAI,OAAO,CAAC,kBAAkB,EAAE;YAC9B,iBAAiB,CAAC,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC;gBAChF,CAAC,CAAC,OAAO,CAAC,kBAAkB;gBAC5B,CAAC,CAAE,IAAA,gBAAS,EAAC,OAAO,CAAC,kBAAkB,CAAY,CAAC;SACvD;QAED,iBAAiB,CAAC,YAAY,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;YACnE,CAAC,CAAE,IAAA,gBAAS,EAAC,IAAI,CAAC,aAAa,CAAY;YAC3C,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;QAEvB,IAAI,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE;YAC3B,iBAAiB,CAAC,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;SACnD;QAED,IAAI,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC,kBAAkB,EAAE;YACnE,iBAAiB,CAAC,kBAAkB,GAAG,OAAO,CAAC,YAAY,CAAC,kBAAkB,CAAC;SAChF;QAED,IAAI,OAAO,CAAC,mBAAmB,EAAE;YAC/B,iBAAiB,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC;SACrE;QAED,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC;QAEhG,IAAI,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC,yBAAyB,EAAE;YAC1E,0BAA0B;YAC1B,iBAAiB,CAAC,yBAAyB,GAAG,OAAO,CAAC,YAAY,CAAC,yBAAyB,CAAC;SAC9F;aAAM,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE;YACjD,iBAAiB,CAAC,yBAAyB,GAAG,CAAC,SAAS,CAAC,CAAC;SAC3D;QAED,MAAM,UAAU,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;QACjD,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACrD,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,IACE,OAAO,CAAC,YAAY;YACpB,OAAO,CAAC,YAAY,CAAC,sBAAsB;YAC3C,CAAC,IAAI,CAAC,yBAAyB,EAC/B;YACA,MAAM,IAAI,uCAA8B,CACtC,iEAAiE,CAClE,CAAC;SACH;QAED,oEAAoE;QACpE,kDAAkD;QAClD,IAAI,CAAC,IAAI,CAAC,gCAAgC,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;YAC7E,IAAI,CAAC,mBAAmB,GAAG,IAAI,wCAAkB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACxE,MAAM,aAAa,GAAuB;gBACxC,wBAAwB,EAAE,KAAK;aAChC,CAAC;YAEF,IAAI,OAAO,CAAC,YAAY,IAAI,IAAI,IAAI,OAAO,OAAO,CAAC,YAAY,CAAC,cAAc,KAAK,QAAQ,EAAE;gBAC3F,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;aAC1B;YAED,IAAI,CAAC,kBAAkB,GAAG,IAAI,0BAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;SACxF;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,IAAI,CAAC,gCAAgC,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAC3E,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,MAAM,IAAI,yBAAiB,CACzB,sHAAsH,CACvH,CAAC;SACH;QACD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,MAAM,IAAI,yBAAiB,CACzB,qHAAqH,CACtH,CAAC;SACH;QAED,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;YACzC,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;SACxC;QAED,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YACvD,OAAO,MAAM,CAAC;SACf;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;YAC1B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE;gBAC/E,MAAM,IAAI,yBAAiB,CACzB,mGAAmG,EACnG,EAAE,KAAK,EAAE,KAAK,EAAE,CACjB,CAAC;aACH;YACD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,KAAc;QAC3B,MAAM,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CACX,EAAU,EACV,GAAa,EACb,UAA0B,EAAE;QAE5B,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,8DAA8D;YAC9D,OAAO,GAAG,CAAC;SACZ;QAED,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAA,gBAAS,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAE3E,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,qBAAqB,CACpD,kCAA0B,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,EAC5C,aAAa,CACd,CAAC;QAEF,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACpC,OAAO,CAAC,EAAE,GAAG,EAAE,CAAC;QAChB,OAAO,CAAC,QAAQ,GAAG,GAAG,CAAC;QAEvB,MAAM,YAAY,GAAG,IAAI,4BAAY,CAAC;YACpC,aAAa,EAAE,KAAK;YACpB,YAAY,EAAE,KAAK;YACnB,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,UAAU,EAAE,IAAI,CAAC,WAAW;SAC7B,CAAC,CAAC;QACH,OAAO,MAAM,YAAY,CAAC,OAAO,CAAW,IAAI,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,QAA+B,EAAE,UAA0B,EAAE;QACzE,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAA,gBAAS,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAEnF,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAE/D,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAEpC,MAAM,YAAY,GAAG,IAAI,4BAAY,CAAC;YACpC,GAAG,OAAO;YACV,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,UAAU,EAAE,IAAI,CAAC,WAAW;SAC7B,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7C,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,OAAO,CAAW,IAAI,EAAE,OAAO,CAAC,CAAC;QACnE,IAAI,cAAc,EAAE;YAClB,wBAAwB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SAC5C;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,oBAAoB;QACxB,OAAO,MAAM,IAAA,iCAAqB,EAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACzD,CAAC;IAED;;;;OAIG;IACH,IAAI,yBAAyB;QAC3B,OAAO,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC;IACpD,CAAC;IAED,MAAM,KAAK,oBAAoB;QAC7B,OAAO,aAAa,CAAC,aAAa,EAAE,CAAC,oBAAoB,CAAC;IAC5D,CAAC;CACF;AApSD,sCAoSC;KA1QE,eAAe;AA4QlB;;;;;;;GAOG;AACH,SAAS,wBAAwB,CAC/B,SAA0D,EAC1D,QAAkB,EAClB,sBAAsB,GAAG,IAAI;IAE7B,IAAI,sBAAsB,EAAE;QAC1B,yEAAyE;QACzE,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YAC7B,QAAQ,GAAG,IAAA,kBAAW,EAAC,QAAQ,CAAC,CAAC;SAClC;QACD,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;YAC9B,MAAM,IAAI,yBAAiB,CAAC,8DAA8D,CAAC,CAAC;SAC7F;KACF;IAED,IAAI,CAAC,SAAS,IAAI,OAAO,SAAS,KAAK,QAAQ;QAAE,OAAO;IACxD,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QACtC,MAAM,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAElC,iEAAiE;QACjE,uCAAuC;QACvC,IAAI,aAAa,IAAI,aAAa,CAAC,SAAS,KAAK,QAAQ,IAAI,aAAa,CAAC,QAAQ,KAAK,CAAC,EAAE;YACzF,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;gBAC9B,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,cAAc,EAAE;oBAC/C,KAAK,EAAE,EAAE;oBACT,YAAY,EAAE,IAAI;oBAClB,UAAU,EAAE,KAAK;oBACjB,QAAQ,EAAE,KAAK;iBAChB,CAAC,CAAC;aACJ;YACD,gDAAgD;YAChD,oEAAoE;YACpE,SAAS,CAAC,cAAc,CAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,8EAA8E;YAC9E,0EAA0E;YAC1E,SAAS;SACV;QAED,wBAAwB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;KAC9D;AACH,CAAC"}