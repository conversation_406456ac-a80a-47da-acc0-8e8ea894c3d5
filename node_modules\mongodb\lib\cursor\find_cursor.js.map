{"version": 3, "file": "find_cursor.js", "sourceRoot": "", "sources": ["../../src/cursor/find_cursor.ts"], "names": [], "mappings": ";;;;AACA,+DAAiE;AACjE,oCAA+E;AAI/E,+CAAwE;AACxE,uEAAyF;AACzF,6CAAqE;AAGrE,kCAAoE;AACpE,oCAA6F;AAC7F,uDAAwE;AAExE,gBAAgB;AAChB,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AACjC,gBAAgB;AAChB,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;AAC3C,gBAAgB;AAChB,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;AAE7C,uCAAuC;AAC1B,QAAA,KAAK,GAAG;IACnB,UAAU;IACV,aAAa;IACb,iBAAiB;IACjB,WAAW;IACX,SAAS;IACT,SAAS;CACD,CAAC;AAEX,cAAc;AACd,MAAa,UAA0B,SAAQ,gCAAuB;IAQpE,gBAAgB;IAChB,YACE,MAAmB,EACnB,SAA2B,EAC3B,SAAmB,EAAE,EACrB,UAAuB,EAAE;QAEzB,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAZpC,gBAAgB;QAChB,QAAc,GAAG,CAAC,CAAC;QAajB,IAAI,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;QACvB,IAAI,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC;QAE9B,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE;YACxB,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,IAAA,iBAAU,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACrD;IACH,CAAC;IAED,KAAK;QACH,MAAM,aAAa,GAAG,IAAA,oBAAY,EAAC,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;QAC5D,OAAO,aAAa,CAAC,OAAO,CAAC;QAC7B,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;YAChE,GAAG,aAAa;SACjB,CAAC,CAAC;IACL,CAAC;IAEQ,GAAG,CAAI,SAA8B;QAC5C,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAkB,CAAC;IAC/C,CAAC;IAED,gBAAgB;IAChB,KAAK,CAAC,WAAW,CAAC,OAAsB;QACtC,MAAM,aAAa,GAAG,IAAI,oBAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;YACrE,GAAG,IAAI,CAAC,aAAa,CAAC;YACtB,GAAG,IAAI,CAAC,aAAa;YACrB,OAAO;SACR,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,MAAM,IAAA,oCAAgB,EAAC,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QAEpE,yDAAyD;QACzD,IAAI,0BAAc,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;YAC/B,IAAI,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC;SACzC;aAAM;YACL,yDAAyD;YACzD,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;SACvF;QAED,kBAAkB;QAClB,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IAC7D,CAAC;IAED,gBAAgB;IACP,KAAK,CAAC,OAAO,CAAC,SAAiB;QACtC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;QACvC,IAAI,WAAW,EAAE;YACf,oEAAoE;YACpE,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC;YACxC,SAAS;gBACP,KAAK,IAAI,KAAK,GAAG,CAAC,IAAI,WAAW,GAAG,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;YAE1F,IAAI,SAAS,IAAI,CAAC,EAAE;gBAClB,IAAI;oBACF,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;iBACpB;gBAAC,OAAO,KAAK,EAAE;oBACd,IAAA,mBAAW,EAAC,KAAK,CAAC,CAAC;oBACnB,yFAAyF;oBACzF,8FAA8F;oBAC9F,8FAA8F;oBAC9F,6FAA6F;oBAC7F,8FAA8F;oBAC9F,8CAA8C;oBAC9C,mGAAmG;oBACnG,mBAAmB;iBACpB;gBACD,OAAO,0BAAc,CAAC,YAAY,CAAC;aACpC;SACF;QAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACvD,2FAA2F;QAC3F,IAAI,0BAAc,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;YAC/B,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC;SAC9D;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;SACtF;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,KAAK,CAAC,OAAsB;QAChC,IAAA,uBAAe,EACb,kKAAkK,CACnK,CAAC;QACF,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;YAChC,MAAM,IAAI,iCAAyB,CAAC,kCAAkC,CAAC,CAAC;SACzE;QACD,OAAO,MAAM,IAAA,oCAAgB,EAC3B,IAAI,CAAC,MAAM,EACX,IAAI,sBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;YAChD,GAAG,IAAI,CAAC,aAAa,CAAC;YACtB,GAAG,IAAI,CAAC,aAAa;YACrB,GAAG,OAAO;SACX,CAAC,CACH,CAAC;IACJ,CAAC;IAED,yCAAyC;IACzC,KAAK,CAAC,OAAO,CAAC,SAAgC;QAC5C,OAAO,MAAM,IAAA,oCAAgB,EAC3B,IAAI,CAAC,MAAM,EACX,IAAI,oBAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;YAC/C,GAAG,IAAI,CAAC,aAAa,CAAC;YACtB,GAAG,IAAI,CAAC,aAAa;YACrB,OAAO,EAAE,SAAS,IAAI,IAAI;SAC3B,CAAC,CACH,CAAC;IACJ,CAAC;IAED,2BAA2B;IAC3B,MAAM,CAAC,MAAgB;QACrB,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,IAAU;QACb,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,GAAG,CAAC,GAAa;QACf,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,GAAG,CAAC,GAAa;QACf,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,SAAS,CAAC,KAAc;QACtB,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,YAAY,CAAC,KAAc;QACzB,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC;QACzC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,gBAAgB,CAAC,IAAY,EAAE,KAA2C;QACxE,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACnB,MAAM,IAAI,iCAAyB,CAAC,GAAG,IAAI,gCAAgC,CAAC,CAAC;SAC9E;QAED,iBAAiB;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE7B,wCAAwC;QACxC,QAAQ,KAAK,EAAE;YACb,KAAK,SAAS;gBACZ,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,GAAG,KAA0B,CAAC;gBACzD,MAAM;YAER,KAAK,SAAS;gBACZ,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,GAAG,KAAgB,CAAC;gBAC/C,MAAM;YAER,KAAK,MAAM;gBACT,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,KAA0B,CAAC;gBACtD,MAAM;YAER,KAAK,KAAK;gBACR,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,GAAG,KAAiB,CAAC;gBAC5C,MAAM;YAER,KAAK,WAAW;gBACd,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,GAAG,KAAe,CAAC;gBAChD,MAAM;YAER,KAAK,KAAK;gBACR,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,GAAG,KAAiB,CAAC;gBAC5C,MAAM;YAER,KAAK,SAAS;gBACZ,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,IAAA,iBAAU,EAAC,KAA0B,CAAC,CAAC;gBAClE,MAAM;YAER,KAAK,OAAO;gBACV,IAAI,CAAC,OAAO,CAAC,GAAG,KAAiB,CAAC;gBAClC,MAAM;YAER,KAAK,WAAW;gBACd,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,GAAG,KAAgB,CAAC;gBACjD,MAAM;YAER,KAAK,aAAa;gBAChB,IAAI,CAAC,aAAa,CAAC,CAAC,YAAY,GAAG,KAAgB,CAAC;gBACpD,MAAM;YAER;gBACE,MAAM,IAAI,iCAAyB,CAAC,2BAA2B,IAAI,EAAE,CAAC,CAAC;SAC1E;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,OAAO,CAAC,KAAa;QACnB,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,cAAc,CAAC,KAAa;QAC1B,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAI,iCAAyB,CAAC,8CAA8C,CAAC,CAAC;SACrF;QAED,IAAI,CAAC,aAAa,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACM,SAAS,CAAC,KAAa;QAC9B,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAI,iCAAyB,CAAC,yCAAyC,CAAC,CAAC;SAChF;QAED,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACH,OAAO,CAAgC,KAAe;QACpD,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC;QACvC,OAAO,IAAgC,CAAC;IAC1C,CAAC;IAED;;;;;OAKG;IACH,IAAI,CAAC,IAAmB,EAAE,SAAyB;QACjD,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE;YAChC,MAAM,IAAI,gCAAwB,CAAC,0CAA0C,CAAC,CAAC;SAChF;QAED,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,IAAA,iBAAU,EAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,YAAY,CAAC,KAAK,GAAG,IAAI;QACvB,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAE1B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE;YAC7B,MAAM,IAAI,iCAAyB,CAAC,qDAAqD,CAAC,CAAC;SAC5F;QAED,oFAAoF;QACpF,IAAI,CAAC,KAAK,EAAE;YACV,IAAI,CAAC,aAAa,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC;YACzC,OAAO,IAAI,CAAC;SACb;QAED,IAAI,CAAC,aAAa,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,SAAS,CAAC,KAAuB;QAC/B,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,KAAa;QACjB,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE;YAChC,MAAM,IAAI,gCAAwB,CAAC,wCAAwC,CAAC,CAAC;SAC9E;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAI,iCAAyB,CAAC,uCAAuC,CAAC,CAAC;SAC9E;QAED,IAAI,CAAC,aAAa,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,KAAa;QAChB,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE;YAChC,MAAM,IAAI,gCAAwB,CAAC,uCAAuC,CAAC,CAAC;SAC7E;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAI,iCAAyB,CAAC,sCAAsC,CAAC,CAAC;SAC7E;QAED,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAhbD,gCAgbC;KA5aE,YAAY"}