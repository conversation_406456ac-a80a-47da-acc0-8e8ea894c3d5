<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Freelancer Sign Up - Freelancer Platform</title>
    <script
      src="https://code.jquery.com/jquery-3.4.1.js"
      integrity="sha256-WpOohJOqMqqyKL9FccASB9O0KwACQJpFTUBLTYOVvVU="
      crossorigin="anonymous"
    ></script>
    <script>
      function submitFreelancerForm() {
        const name = document.getElementById("Name").value;
        const email = document.getElementById("Email").value;
        const password = document.getElementById("Password").value;
        const confirmPassword =
          document.getElementById("ConfirmPassword").value;
        const location = document.getElementById("Location").value;
        const skills = document.getElementById("Skills").value;
        const experience = document.getElementById("Experience").value;
        const hourlyRate = document.getElementById("HourlyRate").value;
        const bio = document.getElementById("Bio").value;

        // Basic validation
        if (
          !name ||
          !email ||
          !password ||
          !confirmPassword ||
          !location ||
          !skills
        ) {
          alert("Please fill in all required fields");
          return;
        }

        if (password.length < 6) {
          alert("Password must be at least 6 characters long");
          return;
        }

        if (password !== confirmPassword) {
          alert("Passwords do not match");
          return;
        }

        // Send data to our backend
        $.ajax({
          url: "/api/signup",
          type: "POST",
          contentType: "application/json",
          data: JSON.stringify({
            name: name,
            email: email,
            password: password,
            role: "freelancer",
            profileData: {
              location: location,
              skills: skills,
              experience: experience,
              hourlyRate: hourlyRate ? parseFloat(hourlyRate) : null,
              bio: bio,
            },
          }),
          success: function (response) {
            alert(
              "Registration successful! Welcome " +
                response.user.name +
                "! Let's complete your freelancer profile."
            );
            // Redirect to freelancer dashboard
            window.location.href = "/freelancer-dashboard.html";
          },
          error: function (xhr) {
            const error = xhr.responseJSON
              ? xhr.responseJSON.error
              : "Registration failed";
            alert("Error: " + error);
          },
        });
      }
    </script>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <div class="role-badge">💼 Freelancer Account</div>
        <h2>Create Your Freelancer Profile</h2>
        <p>
          Join our platform to showcase your skills and connect with clients
        </p>
      </div>

      <form id="freelancerForm">
        <div class="form-section">
          <h3>Basic Information</h3>

          <div class="form-group">
            <label for="Name">Full Name *</label>
            <input
              type="text"
              id="Name"
              name="name"
              placeholder="Enter your full name"
              required
            />
          </div>

          <div class="form-group">
            <label for="Email">Email Address *</label>
            <input
              type="email"
              id="Email"
              name="email"
              placeholder="Enter your email"
              required
            />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="Password">Password *</label>
              <input
                type="password"
                id="Password"
                name="password"
                placeholder="Create a password (min 6 characters)"
                required
              />
            </div>

            <div class="form-group">
              <label for="ConfirmPassword">Confirm Password *</label>
              <input
                type="password"
                id="ConfirmPassword"
                name="confirmPassword"
                placeholder="Confirm your password"
                required
              />
            </div>
          </div>
        </div>

        <div class="form-section">
          <h3>Professional Information</h3>

          <div class="form-group">
            <label for="Location">Location *</label>
            <input
              type="text"
              id="Location"
              name="location"
              placeholder="City, Country"
              required
            />
          </div>

          <div class="form-group">
            <label for="Skills">Skills & Expertise *</label>
            <input
              type="text"
              id="Skills"
              name="skills"
              placeholder="e.g., Web Development, Graphic Design, Content Writing"
              required
            />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="Experience">Experience Level</label>
              <select id="Experience" name="experience">
                <option value="">Select experience</option>
                <option value="beginner">Beginner (0-1 years)</option>
                <option value="intermediate">Intermediate (2-4 years)</option>
                <option value="expert">Expert (5+ years)</option>
              </select>
            </div>

            <div class="form-group">
              <label for="HourlyRate">Hourly Rate (USD)</label>
              <input
                type="number"
                id="HourlyRate"
                name="hourlyRate"
                placeholder="25"
                min="1"
              />
            </div>
          </div>

          <div class="form-group">
            <label for="Bio">Professional Bio</label>
            <textarea
              id="Bio"
              name="bio"
              placeholder="Tell clients about your experience, skills, and what makes you unique..."
              rows="4"
            ></textarea>
          </div>
        </div>

        <div class="button-container">
          <button type="button" onclick="submitFreelancerForm()">
            Create Freelancer Account
          </button>
        </div>
      </form>

      <div class="links">
        <p><a href="signup.html">← Back to role selection</a></p>
        <p>Already have an account? <a href="login.html">Login here</a></p>
      </div>
    </div>
  </body>
  <style>
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      font-family: Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 2rem 0;
    }

    .container {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 700px;
      margin: 1rem;
    }

    .header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .role-badge {
      background: #fff3e0;
      color: #f57c00;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.9rem;
      font-weight: bold;
      display: inline-block;
      margin-bottom: 1rem;
    }

    .header h2 {
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 1.8rem;
    }

    .header p {
      color: #666;
      font-size: 1rem;
    }

    .form-section {
      margin-bottom: 2rem;
      padding-bottom: 1.5rem;
      border-bottom: 1px solid #eee;
    }

    .form-section:last-of-type {
      border-bottom: none;
    }

    .form-section h3 {
      color: #333;
      margin-bottom: 1rem;
      font-size: 1.2rem;
      border-left: 4px solid #667eea;
      padding-left: 1rem;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
    }

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #333;
      font-weight: bold;
    }

    input,
    select,
    textarea {
      width: 100%;
      padding: 12px;
      border: 2px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      transition: border-color 0.3s;
      box-sizing: border-box;
      font-family: Arial, sans-serif;
    }

    input:focus,
    select:focus,
    textarea:focus {
      outline: none;
      border-color: #667eea;
    }

    textarea {
      resize: vertical;
      min-height: 100px;
    }

    .button-container {
      margin-top: 2rem;
    }

    button {
      width: 100%;
      padding: 15px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: transform 0.2s;
    }

    button:hover {
      transform: translateY(-2px);
    }

    .links {
      text-align: center;
      margin-top: 2rem;
      padding-top: 1rem;
      border-top: 1px solid #eee;
    }

    .links p {
      margin: 0.5rem 0;
    }

    .links a {
      color: #667eea;
      text-decoration: none;
    }

    .links a:hover {
      text-decoration: underline;
    }

    @media (max-width: 768px) {
      .form-row {
        grid-template-columns: 1fr;
      }

      .container {
        margin: 0.5rem;
        padding: 1.5rem;
      }

      body {
        padding: 1rem 0;
      }
    }
  </style>
</html>
