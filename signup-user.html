<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>User Sign Up - Freelancer Platform</title>
    <script
      src="https://code.jquery.com/jquery-3.4.1.js"
      integrity="sha256-WpOohJOqMqqyKL9FccASB9O0KwACQJpFTUBLTYOVvVU="
      crossorigin="anonymous"
    ></script>
    <script>
      function submitUserForm() {
        const name = document.getElementById("Name").value;
        const email = document.getElementById("Email").value;
        const password = document.getElementById("Password").value;
        const confirmPassword =
          document.getElementById("ConfirmPassword").value;

        // Basic validation
        if (!name || !email || !password || !confirmPassword) {
          alert("Please fill in all fields");
          return;
        }

        if (password.length < 6) {
          alert("Password must be at least 6 characters long");
          return;
        }

        if (password !== confirmPassword) {
          alert("Passwords do not match");
          return;
        }

        // Send data to our backend
        $.ajax({
          url: "/api/signup",
          type: "POST",
          contentType: "application/json",
          data: JSON.stringify({
            name: name,
            email: email,
            password: password,
            role: "user",
          }),
          success: function (response) {
            alert(
              "Registration successful! Welcome " +
                response.user.name +
                "! Let's find some great freelancers for you."
            );
            // Redirect to user dashboard
            window.location.href = "/user-dashboard.html";
          },
          error: function (xhr) {
            const error = xhr.responseJSON
              ? xhr.responseJSON.error
              : "Registration failed";
            alert("Error: " + error);
          },
        });
      }
    </script>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <div class="role-badge">👤 User Account</div>
        <h2>Create Your User Account</h2>
        <p>Join our platform to find and hire talented freelancers</p>
      </div>

      <form id="userForm">
        <div class="form-group">
          <label for="Name">Full Name</label>
          <input
            type="text"
            id="Name"
            name="name"
            placeholder="Enter your full name"
            required
          />
        </div>

        <div class="form-group">
          <label for="Email">Email Address</label>
          <input
            type="email"
            id="Email"
            name="email"
            placeholder="Enter your email"
            required
          />
        </div>

        <div class="form-group">
          <label for="Password">Password</label>
          <input
            type="password"
            id="Password"
            name="password"
            placeholder="Create a password (min 6 characters)"
            required
          />
        </div>

        <div class="form-group">
          <label for="ConfirmPassword">Confirm Password</label>
          <input
            type="password"
            id="ConfirmPassword"
            name="confirmPassword"
            placeholder="Confirm your password"
            required
          />
        </div>

        <div class="button-container">
          <button type="button" onclick="submitUserForm()">
            Create User Account
          </button>
        </div>
      </form>

      <div class="links">
        <p><a href="signup.html">← Back to role selection</a></p>
        <p>Already have an account? <a href="login.html">Login here</a></p>
      </div>
    </div>
  </body>
  <style>
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      font-family: Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .container {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 500px;
    }

    .header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .role-badge {
      background: #e3f2fd;
      color: #1976d2;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.9rem;
      font-weight: bold;
      display: inline-block;
      margin-bottom: 1rem;
    }

    .header h2 {
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 1.8rem;
    }

    .header p {
      color: #666;
      font-size: 1rem;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #333;
      font-weight: bold;
    }

    input {
      width: 100%;
      padding: 12px;
      border: 2px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      transition: border-color 0.3s;
      box-sizing: border-box;
    }

    input:focus {
      outline: none;
      border-color: #667eea;
    }

    .button-container {
      margin-top: 2rem;
    }

    button {
      width: 100%;
      padding: 15px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: transform 0.2s;
    }

    button:hover {
      transform: translateY(-2px);
    }

    .links {
      text-align: center;
      margin-top: 2rem;
      padding-top: 1rem;
      border-top: 1px solid #eee;
    }

    .links p {
      margin: 0.5rem 0;
    }

    .links a {
      color: #667eea;
      text-decoration: none;
    }

    .links a:hover {
      text-decoration: underline;
    }

    @media (max-width: 768px) {
      .container {
        margin: 1rem;
        padding: 1.5rem;
      }
    }
  </style>
</html>
