<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Save Web Form Data to Spreadsheets</title>
    <!-- <link rel="stylesheet" href="signup.css"> -->
    <script
      src="https://code.jquery.com/jquery-3.4.1.js"
      integrity="sha256-WpOohJOqMqqyKL9FccASB9O0KwACQJpFTUBLTYOVvVU="
      crossorigin="anonymous"
    ></script>
    <script>
      function SubForm() {
        const name = document.getElementById("Name").value;
        const email = document.getElementById("Email").value;
        const password = document.getElementById("Password").value;

        // Basic validation
        if (!name || !email || !password) {
          alert("Please fill in all fields");
          return;
        }

        if (password.length < 6) {
          alert("Password must be at least 6 characters long");
          return;
        }

        // Send data to our backend
        $.ajax({
          url: "/api/signup",
          type: "POST",
          contentType: "application/json",
          data: JSON.stringify({
            name: name,
            email: email,
            password: password,
          }),
          success: function (response) {
            alert("Registration successful! Welcome " + response.user.name);
            // Redirect to home page
            window.location.href = "/";
          },
          error: function (xhr) {
            const error = xhr.responseJSON
              ? xhr.responseJSON.error
              : "Registration failed";
            alert("Error: " + error);
          },
        });
      }
    </script>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h2>Sign Up</h2>
      </div>
      <form id="myForm">
        <input type="text" id="Name" name="name" placeholder="Name" required />
        <input
          type="email"
          id="Email"
          name="email"
          placeholder="Email"
          required
        />
        <input
          type="password"
          id="Password"
          name="password"
          placeholder="Password"
          required
        />

        <div class="button-container">
          <button type="button" onclick="SubForm()">Submit</button>
        </div>
      </form>
    </div>
  </body>
  <style>
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      font-family: Arial, sans-serif;
      background-image: url("black-and-white-hand-drawn-scribble-texture-background-illustration-free-vector.jpg");
      background-size: cover;
      background-attachment: fixed;
    }
    .container {
      width: 400px;
      height: 40%;
      padding: 20px;
      background: #fff;
      text-align: center;
      box-shadow: 0 0 40px rgba(0, 0, 0, 0.1);
      border-radius: 10px;
    }
    .header {
      width: 100%;
      /* padding: 10px; */
      /* background-color: black; */
      color: rgb(7, 1, 1);
      border-radius: 10px 10px 0 0;
      margin-bottom: 10px;
    }
    form {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    form label {
      margin-top: 10px;
    }
    form input {
      margin-bottom: 10px;
      padding: 5px;
      width: 100%;
    }
    .button-container {
      display: flex;
      justify-content: center;
      width: 100%;
      margin-top: 10px;
      margin-top: 50px;
    }
    button {
      padding: 10px 20px;
      background-color: black;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    button:hover {
      background-color: darkgray;
    }
  </style>
</html>
