<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sign Up - Freelancer Platform</title>
    <script
      src="https://code.jquery.com/jquery-3.4.1.js"
      integrity="sha256-WpOohJOqMqqyKL9FccASB9O0KwACQJpFTUBLTYOVvVU="
      crossorigin="anonymous"
    ></script>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h2>Join Our Platform</h2>
        <p>Choose your account type to get started</p>
      </div>

      <div class="role-selection">
        <div class="role-card" onclick="selectRole('user')">
          <div class="role-icon">👤</div>
          <h3>User</h3>
          <p>Looking for freelancers to hire for your projects</p>
          <ul>
            <li>Browse freelancer profiles</li>
            <li>Post project requirements</li>
            <li>Manage hired freelancers</li>
          </ul>
        </div>

        <div class="role-card" onclick="selectRole('freelancer')">
          <div class="role-icon">💼</div>
          <h3>Freelancer</h3>
          <p>Offering your skills and services to clients</p>
          <ul>
            <li>Create professional profile</li>
            <li>Showcase your portfolio</li>
            <li>Connect with potential clients</li>
          </ul>
        </div>
      </div>

      <div class="login-link">
        <p>Already have an account? <a href="login.html">Login here</a></p>
      </div>
    </div>

    <script>
      function selectRole(role) {
        if (role === "user") {
          window.location.href = "signup-user.html";
        } else if (role === "freelancer") {
          window.location.href = "signup-freelancer.html";
        }
      }
    </script>
  </body>
  <style>
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      font-family: Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .container {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 800px;
    }

    .header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .header h2 {
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 2rem;
    }

    .header p {
      color: #666;
      font-size: 1.1rem;
    }

    .role-selection {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .role-card {
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 10px;
      padding: 2rem;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .role-card:hover {
      border-color: #667eea;
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
    }

    .role-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .role-card h3 {
      color: #333;
      margin-bottom: 1rem;
      font-size: 1.5rem;
    }

    .role-card p {
      color: #666;
      margin-bottom: 1rem;
      font-size: 1rem;
    }

    .role-card ul {
      list-style: none;
      padding: 0;
      text-align: left;
    }

    .role-card li {
      color: #555;
      margin-bottom: 0.5rem;
      padding-left: 1rem;
      position: relative;
    }

    .role-card li:before {
      content: "✓";
      color: #28a745;
      font-weight: bold;
      position: absolute;
      left: 0;
    }

    .login-link {
      text-align: center;
      padding-top: 1rem;
      border-top: 1px solid #eee;
    }

    .login-link a {
      color: #667eea;
      text-decoration: none;
    }

    .login-link a:hover {
      text-decoration: underline;
    }

    @media (max-width: 768px) {
      .role-selection {
        grid-template-columns: 1fr;
      }

      .container {
        margin: 1rem;
        padding: 1.5rem;
      }
    }
      align-items: center;
      height: 100vh;
      margin: 0;
      font-family: Arial, sans-serif;
      background-image: url("black-and-white-hand-drawn-scribble-texture-background-illustration-free-vector.jpg");
      background-size: cover;
      background-attachment: fixed;
    }
    .container {
      width: 400px;
      height: 40%;
      padding: 20px;
      background: #fff;
      text-align: center;
      box-shadow: 0 0 40px rgba(0, 0, 0, 0.1);
      border-radius: 10px;
    }
    .header {
      width: 100%;
      /* padding: 10px; */
      /* background-color: black; */
      color: rgb(7, 1, 1);
      border-radius: 10px 10px 0 0;
      margin-bottom: 10px;
    }
    form {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    form label {
      margin-top: 10px;
    }
    form input {
      margin-bottom: 10px;
      padding: 5px;
      width: 100%;
    }
    .button-container {
      display: flex;
      justify-content: center;
      width: 100%;
      margin-top: 10px;
      margin-top: 50px;
    }
    button {
      padding: 10px 20px;
      background-color: black;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    button:hover {
      background-color: darkgray;
    }
  </style>
</html>
