<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>User Dashboard - Find Freelancers</title>
    <script
      src="https://code.jquery.com/jquery-3.4.1.js"
      integrity="sha256-WpOohJOqMqqyKL9FccASB9O0KwACQJpFTUBLTYOVvVU="
      crossorigin="anonymous"
    ></script>
  </head>
  <body>
    <div class="container">
      <!-- Navigation -->
      <nav class="navbar">
        <div class="nav-brand">
          <h2>FreelancerHub</h2>
        </div>
        <div class="nav-links">
          <a href="/" class="active">Home</a>
          <a href="/freelancers-browse.html">Browse All</a>
          <a href="/chat.html">Messages</a>
          <span id="userInfo">Loading...</span>
          <button onclick="logout()" class="logout-btn">Logout</button>
        </div>
      </nav>

      <!-- Welcome Header -->
      <div class="welcome-header">
        <h1>Welcome to Your Dashboard! 👋</h1>
        <p>Discover talented freelancers for your next project</p>
      </div>

      <!-- Quick Stats -->
      <div class="stats-section">
        <div class="stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-info">
            <div class="stat-number" id="totalFreelancers">0</div>
            <div class="stat-label">Available Freelancers</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">💼</div>
          <div class="stat-info">
            <div class="stat-number" id="totalCategories">4</div>
            <div class="stat-label">Service Categories</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">⭐</div>
          <div class="stat-info">
            <div class="stat-number">4.8</div>
            <div class="stat-label">Average Rating</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">💬</div>
          <div class="stat-info">
            <div class="stat-number" id="activeChats">0</div>
            <div class="stat-label">Active Conversations</div>
          </div>
        </div>
      </div>

      <!-- Search Section -->
      <div class="search-section">
        <h3>🔍 Find the Perfect Freelancer</h3>
        <div class="search-container">
          <div class="search-bar">
            <input
              type="text"
              id="searchInput"
              placeholder="Search by skills, name, or location..."
            />
            <button onclick="searchFreelancers()" class="search-btn">
              Search
            </button>
          </div>
          <div class="quick-filters">
            <button class="filter-btn active" onclick="filterByCategory('all')">
              All
            </button>
            <button
              class="filter-btn"
              onclick="filterByCategory('content_writer')"
            >
              Writers
            </button>
            <button
              class="filter-btn"
              onclick="filterByCategory('video_editor')"
            >
              Video Editors
            </button>
            <button class="filter-btn" onclick="filterByCategory('full_stack')">
              Developers
            </button>
            <button
              class="filter-btn"
              onclick="filterByCategory('graphic_designer')"
            >
              Designers
            </button>
          </div>
        </div>
      </div>

      <!-- Featured Freelancers -->
      <div class="featured-section">
        <div class="section-header">
          <h3>⭐ Featured Freelancers</h3>
          <a href="/freelancers-browse.html" class="view-all-btn">View All →</a>
        </div>
        <div class="freelancers-grid" id="featuredFreelancers">
          <!-- Featured freelancers will be loaded here -->
        </div>
      </div>

      <!-- Recent Freelancers -->
      <div class="recent-section">
        <div class="section-header">
          <h3>🆕 Recently Joined</h3>
          <a href="/freelancers-browse.html" class="view-all-btn">View All →</a>
        </div>
        <div class="freelancers-grid" id="recentFreelancers">
          <!-- Recent freelancers will be loaded here -->
        </div>
      </div>

      <!-- Categories Section -->
      <div class="categories-section">
        <h3>📂 Browse by Category</h3>
        <div class="categories-grid">
          <div
            class="category-card"
            onclick="filterByCategory('content_writer')"
          >
            <div class="category-icon">✍️</div>
            <h4>Content Writers</h4>
            <p>Blog posts, articles, copywriting</p>
            <span class="category-count" id="writersCount">0 freelancers</span>
          </div>
          <div class="category-card" onclick="filterByCategory('video_editor')">
            <div class="category-icon">🎬</div>
            <h4>Video Editors</h4>
            <p>Video editing, motion graphics</p>
            <span class="category-count" id="editorsCount">0 freelancers</span>
          </div>
          <div class="category-card" onclick="filterByCategory('full_stack')">
            <div class="category-icon">💻</div>
            <h4>Developers</h4>
            <p>Web development, mobile apps</p>
            <span class="category-count" id="developersCount"
              >0 freelancers</span
            >
          </div>
          <div
            class="category-card"
            onclick="filterByCategory('graphic_designer')"
          >
            <div class="category-icon">🎨</div>
            <h4>Graphic Designers</h4>
            <p>Logo design, branding, UI/UX</p>
            <span class="category-count" id="designersCount"
              >0 freelancers</span
            >
          </div>
        </div>
      </div>

      <!-- Loading indicator -->
      <div id="loading" class="loading" style="display: none">
        <div class="spinner"></div>
        <p>Loading freelancers...</p>
      </div>
    </div>

    <script>
      let currentUser = null;
      let allFreelancers = [];
      let currentFilter = "all";

      // Check authentication
      function checkAuth() {
        $.ajax({
          url: "/api/auth/status",
          type: "GET",
          success: function (response) {
            if (response.authenticated) {
              currentUser = response.user;
              document.getElementById(
                "userInfo"
              ).textContent = `Welcome, ${currentUser.name}`;

              // Check if user is regular user
              if (currentUser.role !== "user") {
                // Redirect non-users to appropriate pages
                if (currentUser.role === "freelancer") {
                  window.location.href = "/freelancer-dashboard.html";
                } else if (currentUser.role === "admin") {
                  window.location.href = "/admin.html";
                }
                return;
              }

              loadDashboardData();
            } else {
              window.location.href = "/login.html";
            }
          },
          error: function () {
            window.location.href = "/login.html";
          },
        });
      }

      // Load dashboard data
      function loadDashboardData() {
        document.getElementById("loading").style.display = "block";

        // Load freelancers
        $.ajax({
          url: "/api/freelancers/browse",
          type: "GET",
          success: function (data) {
            allFreelancers = data;
            updateStats();
            displayFeaturedFreelancers();
            displayRecentFreelancers();
            updateCategoryCounts();
            document.getElementById("loading").style.display = "none";
          },
          error: function () {
            console.error("Failed to load freelancers");
            document.getElementById("loading").style.display = "none";
          },
        });

        // Load chat count
        loadChatCount();
      }

      // Update stats
      function updateStats() {
        document.getElementById("totalFreelancers").textContent =
          allFreelancers.length;
      }

      // Load chat count
      function loadChatCount() {
        $.ajax({
          url: "/api/chats",
          type: "GET",
          success: function (chats) {
            document.getElementById("activeChats").textContent = chats.length;
          },
          error: function () {
            document.getElementById("activeChats").textContent = "0";
          },
        });
      }

      // Display featured freelancers (top rated or most followed)
      function displayFeaturedFreelancers() {
        const featured = allFreelancers
          .sort(
            (a, b) => (b.followers?.length || 0) - (a.followers?.length || 0)
          )
          .slice(0, 3);

        displayFreelancers(featured, "featuredFreelancers");
      }

      // Display recent freelancers
      function displayRecentFreelancers() {
        const recent = allFreelancers
          .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
          .slice(0, 3);

        displayFreelancers(recent, "recentFreelancers");
      }

      // Display freelancers in a container
      function displayFreelancers(freelancers, containerId) {
        const container = document.getElementById(containerId);

        if (freelancers.length === 0) {
          container.innerHTML =
            '<p class="no-freelancers">No freelancers found</p>';
          return;
        }

        container.innerHTML = freelancers
          .map(
            (freelancer) => `
          <div class="freelancer-card" onclick="viewProfile('${
            freelancer._id
          }')">
            <div class="card-header">
              <img src="${
                freelancer.profileData?.profileImage ||
                "https://via.placeholder.com/80"
              }" 
                   alt="${freelancer.name}" class="avatar" />
              <div class="online-badge">●</div>
            </div>
            
            <div class="card-content">
              <h4>${freelancer.name}</h4>
              <p class="title">${
                freelancer.profileData?.skills?.split(",")[0]?.trim() ||
                "Freelancer"
              }</p>
              <p class="location">📍 ${
                freelancer.profileData?.location || "Location not specified"
              }</p>
              
              <div class="rating">
                <span class="stars">⭐⭐⭐⭐⭐</span>
                <span class="rating-text">(4.9)</span>
              </div>
              
              <div class="skills-preview">
                ${
                  freelancer.profileData?.skills
                    ? freelancer.profileData.skills
                        .split(",")
                        .slice(0, 2)
                        .map(
                          (skill) =>
                            `<span class="skill-tag">${skill.trim()}</span>`
                        )
                        .join("")
                    : '<span class="skill-tag">No skills listed</span>'
                }
              </div>
              
              <div class="card-footer">
                <div class="price">
                  <strong>$${
                    freelancer.profileData?.hourlyRate || "N/A"
                  }/hr</strong>
                </div>
                <div class="card-actions">
                  <button onclick="event.stopPropagation(); startChat('${
                    freelancer._id
                  }')" class="btn-chat">
                    💬 Chat
                  </button>
                </div>
              </div>
            </div>
          </div>
        `
          )
          .join("");
      }

      // Update category counts
      function updateCategoryCounts() {
        const counts = {
          content_writer: 0,
          video_editor: 0,
          full_stack: 0,
          graphic_designer: 0,
        };

        allFreelancers.forEach((freelancer) => {
          const skills = freelancer.profileData?.skills?.toLowerCase() || "";
          if (
            skills.includes("content") ||
            skills.includes("writer") ||
            skills.includes("blog")
          ) {
            counts.content_writer++;
          }
          if (
            skills.includes("video") ||
            skills.includes("editor") ||
            skills.includes("motion")
          ) {
            counts.video_editor++;
          }
          if (
            skills.includes("developer") ||
            skills.includes("programming") ||
            skills.includes("web")
          ) {
            counts.full_stack++;
          }
          if (
            skills.includes("design") ||
            skills.includes("graphic") ||
            skills.includes("logo")
          ) {
            counts.graphic_designer++;
          }
        });

        document.getElementById(
          "writersCount"
        ).textContent = `${counts.content_writer} freelancers`;
        document.getElementById(
          "editorsCount"
        ).textContent = `${counts.video_editor} freelancers`;
        document.getElementById(
          "developersCount"
        ).textContent = `${counts.full_stack} freelancers`;
        document.getElementById(
          "designersCount"
        ).textContent = `${counts.graphic_designer} freelancers`;
      }

      // Search freelancers
      function searchFreelancers() {
        const searchTerm = document.getElementById("searchInput").value.trim();
        if (searchTerm) {
          window.location.href = `/freelancers-browse.html?search=${encodeURIComponent(
            searchTerm
          )}`;
        } else {
          window.location.href = "/freelancers-browse.html";
        }
      }

      // Filter by category
      function filterByCategory(category) {
        // Update active filter button
        document.querySelectorAll(".filter-btn").forEach((btn) => {
          btn.classList.remove("active");
        });
        event.target.classList.add("active");

        currentFilter = category;

        if (category === "all") {
          window.location.href = "/freelancers-browse.html";
        } else {
          window.location.href = `/freelancers-browse.html?category=${category}`;
        }
      }

      // View freelancer profile
      function viewProfile(freelancerId) {
        window.location.href = `/freelancer-profile.html?id=${freelancerId}`;
      }

      // Start chat
      function startChat(freelancerId) {
        if (currentUser.id === freelancerId) {
          alert("You cannot chat with yourself");
          return;
        }

        $.ajax({
          url: "/api/chat",
          type: "POST",
          contentType: "application/json",
          data: JSON.stringify({ recipientId: freelancerId }),
          success: function (response) {
            window.location.href = `/chat.html?chatId=${response._id}`;
          },
          error: function () {
            alert("Failed to start chat");
          },
        });
      }

      // Logout
      function logout() {
        $.ajax({
          url: "/api/logout",
          type: "POST",
          success: function () {
            window.location.href = "/login.html";
          },
        });
      }

      // Initialize
      $(document).ready(function () {
        checkAuth();

        // Search on Enter key
        document
          .getElementById("searchInput")
          .addEventListener("keypress", function (e) {
            if (e.key === "Enter") {
              searchFreelancers();
            }
          });
      });
    </script>
  </body>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: "Arial", sans-serif;
      background: #f8f9fa;
      color: #333;
      line-height: 1.6;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    /* Navigation */
    .navbar {
      background: white;
      padding: 1rem 0;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .nav-brand h2 {
      color: #667eea;
      font-size: 1.5rem;
    }

    .nav-links {
      display: flex;
      align-items: center;
      gap: 2rem;
    }

    .nav-links a {
      text-decoration: none;
      color: #333;
      font-weight: 500;
      transition: color 0.3s;
      padding: 0.5rem 1rem;
      border-radius: 5px;
    }

    .nav-links a:hover,
    .nav-links a.active {
      color: #667eea;
      background: #f0f4ff;
    }

    .logout-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 5px;
      cursor: pointer;
    }

    /* Welcome Header */
    .welcome-header {
      text-align: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 3rem 2rem;
      border-radius: 15px;
      margin-bottom: 2rem;
    }

    .welcome-header h1 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }

    .welcome-header p {
      font-size: 1.2rem;
      opacity: 0.9;
    }

    /* Stats Section */
    .stats-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 3rem;
    }

    .stat-card {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 1.5rem;
      transition: transform 0.3s;
    }

    .stat-card:hover {
      transform: translateY(-5px);
    }

    .stat-icon {
      font-size: 3rem;
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      color: white;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: bold;
      color: #333;
      margin-bottom: 0.5rem;
    }

    .stat-label {
      color: #666;
      font-size: 0.9rem;
    }

    /* Search Section */
    .search-section {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      margin-bottom: 3rem;
    }

    .search-section h3 {
      margin-bottom: 1.5rem;
      color: #333;
      font-size: 1.3rem;
    }

    .search-bar {
      display: flex;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .search-bar input {
      flex: 1;
      padding: 1rem;
      border: 2px solid #ddd;
      border-radius: 10px;
      font-size: 1rem;
      transition: border-color 0.3s;
    }

    .search-bar input:focus {
      outline: none;
      border-color: #667eea;
    }

    .search-btn {
      padding: 1rem 2rem;
      background: #667eea;
      color: white;
      border: none;
      border-radius: 10px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s;
    }

    .search-btn:hover {
      background: #5a6fd8;
      transform: translateY(-2px);
    }

    .quick-filters {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .filter-btn {
      padding: 0.75rem 1.5rem;
      border: 2px solid #ddd;
      background: white;
      border-radius: 25px;
      cursor: pointer;
      transition: all 0.3s;
      font-weight: 500;
    }

    .filter-btn:hover,
    .filter-btn.active {
      border-color: #667eea;
      background: #667eea;
      color: white;
    }

    /* Section Headers */
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
    }

    .section-header h3 {
      color: #333;
      font-size: 1.5rem;
    }

    .view-all-btn {
      color: #667eea;
      text-decoration: none;
      font-weight: 600;
      transition: color 0.3s;
    }

    .view-all-btn:hover {
      color: #5a6fd8;
    }

    /* Featured & Recent Sections */
    .featured-section,
    .recent-section {
      margin-bottom: 3rem;
    }

    .freelancers-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .freelancer-card {
      background: white;
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      cursor: pointer;
    }

    .freelancer-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    .card-header {
      position: relative;
      text-align: center;
      padding: 2rem 1rem 1rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      object-fit: cover;
      border: 4px solid white;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .online-badge {
      position: absolute;
      top: 2.5rem;
      right: calc(50% - 35px);
      color: #28a745;
      font-size: 1.2rem;
    }

    .card-content {
      padding: 1.5rem;
    }

    .card-content h4 {
      font-size: 1.2rem;
      margin-bottom: 0.5rem;
      color: #333;
      text-align: center;
    }

    .title {
      color: #667eea;
      font-weight: 600;
      text-align: center;
      margin-bottom: 0.5rem;
    }

    .location {
      color: #666;
      text-align: center;
      margin-bottom: 1rem;
      font-size: 0.9rem;
    }

    .rating {
      text-align: center;
      margin-bottom: 1rem;
    }

    .stars {
      color: #ffc107;
      margin-right: 0.5rem;
    }

    .rating-text {
      color: #666;
      font-size: 0.9rem;
    }

    .skills-preview {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      justify-content: center;
      margin-bottom: 1.5rem;
    }

    .skill-tag {
      background: #e3f2fd;
      color: #1976d2;
      padding: 0.3rem 0.8rem;
      border-radius: 15px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 1rem;
      border-top: 1px solid #eee;
    }

    .price {
      color: #28a745;
      font-size: 1.1rem;
    }

    .btn-chat {
      padding: 0.5rem 1rem;
      background: #17a2b8;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 0.9rem;
      transition: all 0.3s;
    }

    .btn-chat:hover {
      background: #138496;
    }

    /* Categories Section */
    .categories-section {
      margin-bottom: 3rem;
    }

    .categories-section h3 {
      color: #333;
      font-size: 1.5rem;
      margin-bottom: 2rem;
    }

    .categories-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .category-card {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;
    }

    .category-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    .category-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .category-card h4 {
      color: #333;
      margin-bottom: 0.5rem;
    }

    .category-card p {
      color: #666;
      margin-bottom: 1rem;
      font-size: 0.9rem;
    }

    .category-count {
      color: #667eea;
      font-weight: 600;
      font-size: 0.9rem;
    }

    /* Loading */
    .loading {
      text-align: center;
      padding: 3rem;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    .no-freelancers {
      text-align: center;
      color: #666;
      padding: 2rem;
      grid-column: 1 / -1;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .container {
        padding: 0 15px;
      }

      .welcome-header h1 {
        font-size: 2rem;
      }

      .welcome-header p {
        font-size: 1rem;
      }

      .stats-section {
        grid-template-columns: repeat(2, 1fr);
      }

      .freelancers-grid {
        grid-template-columns: 1fr;
      }

      .categories-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .nav-links {
        flex-direction: column;
        gap: 1rem;
      }

      .search-bar {
        flex-direction: column;
      }

      .quick-filters {
        justify-content: center;
      }

      .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
      }
    }

    @media (max-width: 480px) {
      .stats-section {
        grid-template-columns: 1fr;
      }

      .categories-grid {
        grid-template-columns: 1fr;
      }

      .stat-card {
        flex-direction: column;
        text-align: center;
      }
    }
  </style>
</html>
